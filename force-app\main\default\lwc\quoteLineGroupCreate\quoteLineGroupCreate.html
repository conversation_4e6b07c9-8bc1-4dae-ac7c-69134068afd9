<template>
    <!-- 加载中状态 -->
    <template if:true={isLoading}>
        <div class="spinner-container">
            <lightning-spinner alternative-text="加载中" size="medium"></lightning-spinner>
        </div>
    </template>
    
    <!-- 正常内容显示 -->
    <template if:false={isLoading}>
        <!-- 顶部操作区域 -->
         <lightning-card class="slds-m-bottom_medium"  variant="Narrow"  title="报价信息" icon-name="standard:account">
            <template if:true={isEditMode}>
           <div style="position: relative;">
            <div class="slds-grid slds-gutters">
                <template if:true={quoteInfo}>
                    <div class="slds-col slds-size_1-of-1">
                        <div class="slds-grid slds-wrap">
                            <div class="slds-col slds-size_1-of-5 slds-p-horizontal_small" style="width: 10rem;">
                                <div class="slds-text-title slds-text-color_weak">报价编号</div>
                                <div class="slds-text-body_regular">{quoteInfo.quoteNumber}</div>
                            </div>
                            <!-- <div class="slds-col slds-size_1-of-5 slds-p-horizontal_small" style="width: 10rem;">
                                <div class="slds-text-title slds-text-color_weak">有效期</div>
                                <div class="slds-text-body_regular">
                                    <lightning-formatted-date-time value={quoteInfo.endDate} year="numeric" month="numeric" day="numeric"></lightning-formatted-date-time>
                                </div>
                            </div> -->
                            <div class="slds-col slds-size_1-of-5 slds-p-horizontal_small" style="width: 15rem;">
                                <div class="slds-text-title slds-text-color_weak">商机名</div>
                                <template if:true={quoteInfo.opportunity}>
                                    <lightning-record-view-form
                                        object-api-name="Opportunity" 
                                        record-id={quoteInfo.opportunity}
                                        density="compact"
                                        class="opportunity-form"
                                    >
                                        <lightning-output-field style="color: #5c5c5c" field-name="Name" variant="label-hidden"></lightning-output-field>
                                    </lightning-record-view-form>
                                </template>
                            </div>
                            <div class="slds-col slds-size_1-of-5 slds-p-horizontal_small" style="width: 10rem;">
                                <div class="slds-text-title slds-text-color_weak">收款账户</div>
                                <div class="slds-text-body_regular">{quoteInfo.accountName}</div>
                            </div>
                            <!-- <div class="slds-col slds-size_1-of-5 slds-p-horizontal_small" style="width: 10rem;">
                                <div class="slds-text-title slds-text-color_weak">总价</div>
                                <div class="slds-text-body_regular">
                                    <lightning-formatted-number value={quoteInfo.totalPrice} format-style="currency" currency-code="SGD"></lightning-formatted-number>
                                </div>
                            </div> -->
                        </div>
                    </div>
                </template>
            </div>
           </div>
         </template>
          <div class="slds-grid slds-gutters slds-m-bottom_medium">
            <div class="slds-col"></div>
            <div class="slds-col slds-text-align_right" style="margin-right: 20px;  right: 2px; top: -60px;position: absolute;">
                <template if:true={isEditMode}>
                    <!-- 临时产品提示信息 -->
                    <!-- <template if:true={hasTempProducts}>
                        <div class="slds-notify slds-notify_alert slds-theme_warning slds-m-bottom_small" style="margin-right: 10px; display: inline-block; padding: 0.5rem;">
                            <span class="slds-assistive-text">警告</span>
                            <lightning-icon icon-name="utility:warning" alternative-text="警告" size="x-small" class="slds-m-right_x-small"></lightning-icon>
                            有 {tempProductsCount} 个产品等待保存
                        </div>
                    </template> -->

                    <lightning-button label="返回" icon-name="utility:back" onclick={handleBack} class="slds-m-right_small"></lightning-button>
                    <!-- 只有MAAS产品线和AIsearch才显示添加产品组 -->
                    <template if:true={isAISearchProduct}> 
                         <lightning-button label="添加产品组" icon-name="utility:add" onclick={handleAddGroup} class="slds-m-right_small"></lightning-button>    
                    </template>
                    <template if:true={isMassProduct}> 
                         <lightning-button label="添加产品组" icon-name="utility:add" onclick={handleAddGroup} class="slds-m-right_small"></lightning-button>    
                    </template>
                   
                    <lightning-button label="添加产品" onclick={handleAddSingleProduct} class="slds-m-right_small"></lightning-button>
                    <lightning-button label="保存" variant="brand" icon-name="utility:save" onclick={handleSubmit} disabled={isSubmitting}>
                        <template if:true={isSubmitting}>
                            <lightning-spinner alternative-text="Saving..." size="small" variant="brand"></lightning-spinner>
                        </template>
                    </lightning-button>

                </template>
            </div>
        </div>
    </lightning-card>

        <!-- 单产品列表 -->
        <div class="slds-box slds-theme_default slds-m-bottom_medium">
            <template if:true={singleProducts.length}>
            <div class="slds-grid slds-gutters slds-m-bottom_small">
                <div class="slds-col">
                    <div class="slds-text-heading_medium">单产品报价</div>
                    <!-- 单产品三级产品标签 -->
                    <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 0.5rem; margin-top: 0.5rem;">
                        <template if:true={isEditMode}>
                            <!-- 显示前4个三级产品标签 -->
                            <template for:each={singleProductTags.levelThreeProducts} for:item="level3Product">
                                <lightning-pill key={level3Product.id}
                                                label={level3Product.name}
                                                onremove={handleRemoveLevelThreeFromSingle}
                                                data-level3-name={level3Product.name}>
                                </lightning-pill>
                            </template>

                            <!-- 如果超过4个标签，显示"更多"文字 -->
                            <template if:true={singleProductTags.hasMoreTags}>
                                <span
                                    class="more-tags-text"
                                    onclick={handleShowMoreTagsForSingle}
                                    style="color: #0176d3; cursor: pointer; font-size: 0.875rem; text-decoration: underline; margin-left: 0.5rem;">
                                    {singleProductTags.moreTagsLabel}
                                </span>
                            </template>
                        </template>
                    </div>
                </div>
                <div class="slds-col slds-text-align_right">
                    <template if:true={isEditMode}>
                         <template if:false={isMassProduct}>
                            <lightning-button label="删除产品" variant="destructive" onclick={handleDeleteSingleProduct} class="slds-m-right_small"></lightning-button>
                        </template>
                        <!-- <lightning-button label="删除产品" variant="destructive" onclick={handleDeleteSingleProduct} class="slds-m-right_small"></lightning-button> -->
                        <!-- <lightning-button label="调试复选框" onclick={debugCheckboxState} variant="neutral" class="slds-m-right_small"></lightning-button> -->
                    </template>
                </div>
            </div>
            
            <!-- 单产品datatable -->
            
                <div style="max-width: 120rem; " class="slds-scope">
                    <c-custom-datatable
                    key-field="id"
                    data={singleProducts}
                    columns={singleProductColumns}
                    hide-checkbox-column={hideCheckboxColumn}
                    show-row-number-column={showRowNumberColumn}
                    onrowselection={handleSingleProductRowSelection}
                    onrowaction={handleRowAction}
                    onsave={handleSingleProductCellChange}>
                </c-custom-datatable>
                    <!-- <lightning-datatable
                        key-field="id"
                        data={singleProducts}
                        columns={singleProductColumns}
                        hide-checkbox-column={hideCheckboxColumn}
                        show-row-number-column={showRowNumberColumn}
                        selected-rows={selectedSingleProducts}
                        onrowselection={handleSingleProductRowSelection}
                        onrowaction={handleRowAction}
                        onsave={handleSingleProductCellChange}>
                    </lightning-datatable> -->
                </div>
            </template>
            
            <!-- 没有单产品时显示提示 -->
            <!-- <template if:false={singleProducts.length}>
                <div class="slds-text-align_center slds-p-around_medium slds-text-color_weak">
                    暂无单产品数据
                </div>
            </template> -->
        </div>
        
        
        <!-- 产品组列表 -->
        <template for:each={productGroups} for:item="group" for:index="groupIndex">
            <div class="slds-box slds-theme_default slds-m-bottom_medium" key={group.id}>
                <!-- 产品组标题 -->
                <div class="slds-grid slds-gutters slds-m-bottom_small">
                    <div class="slds-col">
                        <div class="slds-text-heading_medium">产品组{group.groupNumber}
                             <template if:true={isEditMode} >
                                <lightning-button-icon style="margin-left: 10px;" icon-name="utility:delete" alternative-text="删除产品组" 
                                                    variant="border-filled" onclick={handleDeleteGroup} 
                                                    data-group-index={groupIndex}></lightning-button-icon>
                            
                            </template>
                        </div>
                        <div class="slds-text-body_small slds-text-color_weak">
                        </div>
                    </div>
                    <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 0.5rem;">
                        <template if:true={isEditMode}>
                            <!-- 显示前4个三级产品标签 -->
                            <template for:each={group.levelThreeProducts} for:item="level3Product">
                                <lightning-pill key={level3Product.id}
                                                label={level3Product.name}
                                                onremove={handleRemoveLevelThree}
                                                data-group-index={groupIndex}
                                                data-level3-name={level3Product.name}>
                                </lightning-pill>
                            </template>

                            <!-- 如果超过4个标签，显示"更多"文字 -->
                            <template if:true={group.hasMoreTags}>
                                <span
                                    class="more-tags-text"
                                    onclick={handleShowMoreTags}
                                    data-group-index={groupIndex}
                                    style="color: #0176d3; cursor: pointer; font-size: 0.875rem; text-decoration: underline; margin-left: 0.5rem;">
                                    {group.moreTagsLabel}
                                </span>
                            </template>
                        </template>
                    </div>
                    <div class="slds-col slds-text-align_right">
                         <template if:true={isEditMode}>
                            <span style="margin-right: 1rem; vertical-align: middle;">
                                <span style="color: #ea001e;">*</span>报价方式：
                                <span style="font-weight: normal; color: #222;">{group.quoteTypeLabel}
                                    <template if:false={group.quoteTypeValue}>
                                        <!-- <template if:true={group.hasFixedAmountQuote}>固定金额</template> -->
                                        <template if:true={group.hasProductDiscountQuote}>产品折扣</template>
                                        <template if:true={group.hasSharedLadderAmountDiscountZone}>阶梯金额折扣</template>
                                        <!-- <template if:true={group.hasSharedLadderAmountDiscountDown}>共享阶梯金额折扣落区</template> -->
                                        <template if:true={group.hasSharedLadderUsagePriceDown}>阶梯用量单价</template>
                                        <template if:true={group.hasMinAmountSharedLadderAmountDiscountDown}>保底金额+共享阶梯金额折扣</template>
                                        <template if:true={group.hasMinUnitPriceSharedLadderUsagePriceDown}>单价*数量+阶梯用量单价</template>
                                    </template>
                                </span>
                            </span>
                            <lightning-button label="配置报价"  onclick={handleConfigureQuote} data-group-index={groupIndex}></lightning-button>
                        </template>
                        <template if:true={isEditMode}>
                            <lightning-button label="添加产品"  onclick={handleAddProductClick} style="margin-left: 10px;" data-group-index={groupIndex}></lightning-button>
                        </template>
                         <template if:true={isEditMode}>
                            <template if:false={isMassProduct}>
                                <lightning-button label="删除产品" variant="destructive" style="margin-left: 10px;"  onclick={handleDeleteLine} data-group-index={groupIndex}></lightning-button>
                            </template>
                            <!-- <lightning-button label="删除产品" variant="destructive" style="margin-left: 10px;"  onclick={handleDeleteLine} data-group-index={groupIndex}></lightning-button> -->
                        </template>
                    </div>
                    <!-- <div class="slds-col slds-text-align_center">
                        <template if:true={isEditMode}>
                            <lightning-button label="Add Quote Type" onclick={handleShowQuoteTypeModal} data-group-index={groupIndex} icon-name="utility:add" ></lightning-button>
                        </template>
                    </div> -->
                  
                    <!-- <template if:true={isEditMode}>
                        <div class="slds-col slds-text-align_right">
                            <lightning-button-icon icon-name="utility:delete" alternative-text="删除产品组" 
                                                variant="border-filled" onclick={handleDeleteGroup} 
                                                data-group-index={groupIndex}></lightning-button-icon>
                        </div>
                    </template> -->
                    
                </div>
                
                <!-- 产品列表 -->
                <div>
                    <!-- 使用lightning-datatable展示产品数据 -->
                    <template if:true={group.products.length}>
                        <div key={group.id} data-group-index={groupIndex}>


                              <c-custom-datatable
                                data-id={group.id}
                                key-field="id"
                                key={isEditMode}
                                data={group.products}
                                columns={productColumns}
                                hide-checkbox-column={hideCheckboxColumn}
                                show-row-number-column={showRowNumberColumn}
                                onrowselection={handleRowSelection}
                                onrowaction={handleProductGroupRowAction}
                                onsave={handleCellChange}>
                            </c-custom-datatable>
                            <!-- <lightning-datatable
                                data-id={group.id}
                                key-field="id"
                                key={isEditMode}
                                data={group.products}
                                columns={productColumns}
                                hide-checkbox-column={hideCheckboxColumn}
                                show-row-number-column={showRowNumberColumn}
                                selected-rows={selectedRows}
                                onrowselection={handleRowSelection}
                               
                                onsave={handleCellChange}>
                            </lightning-datatable> -->
                        </div>
                    </template>
                    
                      <!-- 报价方式区域 -->
                <div class="slds-m-top_medium" if:false={isEditMode}>
                    <div class="slds-grid slds-gutters slds-m-bottom_small">
                        <div class="slds-col">
                           
                            <div class="slds-text-body_small slds-text-color_weak">
                                <template if:true={isEditMode}>
                                    <lightning-combobox label="报价方式" value={group.quoteTypeValue} data-id={groupIndex} 
                                    options={quoteTypeOptions} onchange={handleQuoteTypeChange} disabled={group.hasAnyQuoteType}></lightning-combobox>

                                </template>
                                <template if:false={isEditMode}>
                                    <!-- 只读模式下显示当前报价方式 -->
                                    <div class="slds-text-heading_small slds-text-color_default">
                                        <template if:true={group.quoteTypeValue}>
                                            {group.quoteTypeLabel}
                                        </template>
                                        <template if:false={group.quoteTypeValue}>
                                            <!-- <template if:true={group.hasFixedAmountQuote}>固定金额</template> -->
                                            <template if:true={group.hasProductDiscountQuote}>产品折扣</template>
                                            <template if:true={group.hasSharedLadderAmountDiscountZone}>阶梯金额折扣</template>
                                            <!-- <template if:true={group.hasSharedLadderAmountDiscountDown}>共享阶梯金额折扣落区</template> -->
                                            <template if:true={group.hasSharedLadderUsagePriceDown}>阶梯用量单价</template>
                                            <template if:true={group.hasMinAmountSharedLadderAmountDiscountDown}>保底金额+共享阶梯金额折扣</template>
                                            <template if:true={group.hasMinUnitPriceSharedLadderUsagePriceDown}>单价*数量+阶梯用量单价</template>
                                        </template>
                                    </div>
                                </template>
                                
                            </div>
                        </div>
                       
                    </div>
                    
                    <!-- 报价方式卡片区域 -->
                    <div class="slds-box slds-theme_default">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div class="slds-text-heading_small slds-text-color_default">组合{group.groupNumber}报价方式</div>
                            <template if:true={group.hasAnyQuoteType}>
                                <div if:true={isEditMode}>
                                    <lightning-button-icon icon-name="utility:delete" alternative-text="删除报价方式" 
                                                        variant="border-filled" size="small" 
                                                        data-group-index={groupIndex}
                                                        onclick={handleDeleteAllQuoteTypes}></lightning-button-icon>
                                </div>
                            </template>
                        </div>
                        
                    <div class="slds-grid slds-wrap slds-gutters">
                        <!-- 保底金额卡片 -->
                        <template if:true={group.hasMinimumAmountQuote}>
                                <div class="slds-col slds-size_1-of-3 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px;">
                                    <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <div class="slds-text-heading_small slds-text-color_default">保底金额</div>
                                    </div>
                                    
                                        <!-- 保底金额表单 - 表格样式 -->
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                    <th scope="col" class="slds-text-align_center">保底金额（不含税）</th>
                                                    <th scope="col" class="slds-text-align_center">保底类型</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="slds-text-align_center">
                                                <template if:true={isEditMode}>
                                                    <lightning-input type="number" step="0.01"
                                                                  value={group.minimumAmount}
                                                                  data-field="minimumAmount"
                                                                  data-group-index={groupIndex}
                                                                  onchange={handleMinimumAmountChange}
                                                                          variant="label-hidden"
                                                                  required></lightning-input>
                                                </template>
                                                <template if:false={isEditMode}>
                                                    <div>{group.minimumAmount}</div>
                                                </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-combobox
                                                                value={group.minimumGuaranteeType}
                                                                options={minimumGuaranteeTypeOptions}
                                                                onchange={handleMinimumGuaranteeTypeChange}
                                                                variant="label-hidden"
                                                                data-group-index={groupIndex}>
                                                            </lightning-combobox>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{group.minimumGuaranteeTypeLabel}</div>
                                                        </template>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                </div>
                            </div>
                        </template>
                        
                            <!-- 单价*数量卡片 -->
                            <template if:true={group.hasMinimumUnitPriceQuote}>
                                <div class="slds-col slds-size_1-of-3 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px;">
                                    <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                            <div class="slds-text-heading_small slds-text-color_default">单价*数量</div>
                                    </div>
                                    
                                        <!-- 单价*数量表单 - 表格样式 -->
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                        <thead>
                                            <tr class="slds-line-height_reset">
                                                    <th scope="col" class="slds-text-align_center">单价（不含税）</th>
                                                    <th scope="col" class="slds-text-align_center">数量</th>
                                                    <th scope="col" class="slds-text-align_center">是否赠送</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-input type="number" step="0.000001"
                                                                          value={group.minimumUnitPrice}
                                                                          data-field="minimumUnitPrice"
                                                                          data-group-index={groupIndex}
                                                                          onchange={handleMinimumUnitPriceChange}
                                                                          onblur={handleUnitPriceBlur}
                                                                          
                                                                          variant="label-hidden"
                                                                          required></lightning-input>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{group.minimumUnitPrice}</div>
                                                        </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-input type="number" step="0.000001"
                                                                          value={group.minimumQuantity}
                                                                          data-field="minimumQuantity"
                                                                          data-group-index={groupIndex}
                                                                          onchange={handleMinimumUnitPriceChange}
                                                                         
                                                                          variant="label-hidden"
                                                                          required></lightning-input>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{group.minimumQuantity}</div>
                                                        </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-combobox
                                                                value={group.isGift}
                                                                options={isGiftOptions}
                                                                onchange={handleIsGiftChange}
                                                                variant="label-hidden"
                                                                data-group-index={groupIndex}>
                                                            </lightning-combobox>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{group.isGiftLabel}</div>
                                                        </template>
                                                    </td>
                                                    <!-- <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-input type="number" step="1"
                                                                          value={group.expandMultiple}
                                                                          data-field="expandMultiple"
                                                                          data-group-index={groupIndex}
                                                                          onchange={handleMinimumUnitPriceChange}
                                                                          variant="label-hidden"
                                                                          required
                                                                          min="1"
                                                                          max="99999"
                                                                          message-when-value-missing="扩大倍数为必填项"
                                                                          message-when-range-overflow="扩大倍数不能超过99999"
                                                                          message-when-range-underflow="扩大倍数不能小于1"></lightning-input>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{group.expandMultiple}</div>
                                                        </template>
                                                    </td> -->
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                            </template>
                                            
                            <!-- 固定金额卡片 - 已注释掉 -->
                            <!-- <template if:true={group.hasFixedAmountQuote}>
                                <div class="slds-col slds-size_1-of-3 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                            <div class="slds-text-heading_small slds-text-color_default">固定金额</div>
                                        </div>

                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                    <th scope="col" class="slds-text-align_center">固定单价</th>
                                                    <th scope="col" class="slds-text-align_center">固定数量</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-input type="number" step="0.01"
                                                                          value={group.fixedAmount}
                                                                          data-field="fixedAmount"
                                                                          data-group-index={groupIndex}
                                                                          onchange={handleFixedUsageChange}
                                                                          variant="label-hidden"
                                                                          required></lightning-input>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{group.fixedAmount}</div>
                                                        </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-input type="number" step="1"
                                                                          value={group.fixedUsage}
                                                                          data-field="fixedUsage"
                                                                          data-group-index={groupIndex}
                                                                          onchange={handleFixedUsageChange}
                                                                          variant="label-hidden"
                                                                          required></lightning-input>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{group.fixedUsage}</div>
                                                        </template>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </template> -->
                            
                            <!-- 产品折扣卡片 -->
                            <template if:true={group.hasProductDiscountQuote}>
                                <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px; overflow-x: auto;">
                                        <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                            <div class="slds-text-heading_small slds-text-color_default">产品折扣</div>
                                        </div>

                                        <!-- 产品折扣表单 - 分两行布局 -->
                                        <!-- 第一行：折扣系数和折扣类型 -->
                                        <template if:false={isMassProduct}>
                                            <!-- 非MAAS产品显示折扣系数和折扣类型 -->
                                            <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small" style="min-width: 400px;">
                                                <thead>
                                                    <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center" style="min-width: 150px;">折扣系数（%）</th>
                                                        <th scope="col" class="slds-text-align_center" style="min-width: 150px;">折扣类型</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <span style="color: #ea001e;">*</span>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={group.discountCoefficient}
                                                                              data-field="discountCoefficient"
                                                                                  data-group-index={groupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              max="100"
                                                                              min="1"
                                                                              required></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{group.discountCoefficient}</div>
                                                            </template>
                                                        </td>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-combobox
                                                                    value={group.discountType}
                                                                    options={discountTypeOptions}
                                                                    onchange={handleDiscountTypeChange}
                                                                    variant="label-hidden"
                                                                    data-group-index={groupIndex}>
                                                                </lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{group.discountTypeLabel}</div>
                                                            </template>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </template>

                                        <template if:true={isMassProduct}>
                                            <!-- MAAS产品只显示折扣系数 -->
                                            <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small" style="min-width: 200px;">
                                                <thead>
                                                    <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center" style="min-width: 150px;">折扣系数（%）</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <span style="color: #ea001e;">*</span>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={group.discountCoefficient}
                                                                              data-field="discountCoefficient"
                                                                                  data-group-index={groupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              max="100"
                                                                              min="1"
                                                                              required></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{group.discountCoefficient}</div>
                                                            </template>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </template>

                                        <!-- 第二行：固定返利、现金减免、Credit - 仅非MAAS产品显示 -->
                                        <template if:false={isMassProduct}>
                                            <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small" style="min-width: 600px;">
                                                <thead>
                                                    <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center" style="min-width: 120px;">固定返利（%）</th>
                                                        <th scope="col" class="slds-text-align_center" style="min-width: 120px;">现金减免（%）</th>
                                                        <th scope="col" class="slds-text-align_center" style="min-width: 120px;">Credit（%）</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={group.fixedRebate}
                                                                              data-field="fixedRebate"
                                                                              data-group-index={groupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              max="99.99"
                                                                              min="0.00"
                                                                              required></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{group.fixedRebate}</div>
                                                            </template>
                                                        </td>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={group.cashReduce}
                                                                              data-field="cashReduce"
                                                                                  data-group-index={groupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              required></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{group.cashReduce}</div>
                                                            </template>
                                                        </td>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={group.credit}
                                                                              data-field="credit"
                                                                                 data-group-index={groupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              required></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{group.credit}</div>
                                                            </template>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </template>
                                    </div>
                                </div>
                            </template>
                            
                            <!-- 共享阶梯金额折扣分区 - 已注释掉 -->
                            
                            <template if:true={group.hasSharedLadderAmountDiscountZone}>
                                <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 10px; border-radius: 8px; height: 100%;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                            <div class="slds-text-heading_small slds-text-color_default">阶梯金额折扣</div>
                                        </div>

                                        <div class="slds-m-top_small">
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center">下限</th>
                                                        <th scope="col" class="slds-text-align_center">上限</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">单位</th> -->
                                                        <th scope="col" class="slds-text-align_center">折扣（%）</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">计算方式</th> -->
                                                        <template if:true={isEditMode}>
                                                            <th scope="col" class="slds-text-align_center">操作</th>
                                                        </template>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <template for:each={group.tiers} for:item="tier" for:index="tierIndex">
                                                    <tr key={tier.id}>
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-input type="number" step="1"
                                                                                    value={tier.lowerBound}
                                                                                data-field="lowerBound"
                                                                                data-group-index={groupIndex}
                                                                                data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"></lightning-input>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.lowerBound}
                                                                </template>
                                                            </td>
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-input type="number" step="1"
                                                                                    value={tier.upperBound}
                                                                                data-field="upperBound"
                                                                                data-group-index={groupIndex}
                                                                                data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"></lightning-input>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.upperBound}
                                                                </template>
                                                            </td>
                                                            <!-- <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-combobox
                                                                                    value={tier.unit}
                                                                                    options={unitOptions}
                                                                                    data-field="unit"
                                                                                    data-group-index={groupIndex}
                                                                                    data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"
                                                                                    disabled={tier.unitNotEditable}></lightning-combobox>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.unit}
                                                                </template>
                                                            </td> -->
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-input type="number" step="0.01"
                                                                                    value={tier.discount}
                                                                                data-field="discount"
                                                                                data-group-index={groupIndex}
                                                                                data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"></lightning-input>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.discount}
                                                                </template>
                                                            </td>
                                                            <!-- <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-combobox
                                                                                    value={tier.calculationMethod}
                                                                                    options={calculationMethodOptions}
                                                                                    data-field="calculationMethod"
                                                                                    data-group-index={groupIndex}
                                                                                    data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"
                                                                                    disabled={tier.calculationMethodNotEditable}></lightning-combobox>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.calculationMethod}
                                                                </template>
                                                            </td> -->
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                    <template if:true={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="error"
                                                                                        size="small"
                                                                                        data-group-index={groupIndex}
                                                                                        data-tier-index={tierIndex}
                                                                                        onclick={handleDeleteTier}></lightning-button-icon>
                                                                    </template>
                                                                    <template if:false={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="border-filled"
                                                                                        size="small"
                                                                                        disabled="true"
                                                                                        data-group-index={groupIndex}
                                                                                        data-tier-index={tierIndex}></lightning-button-icon>
                                                                    </template>
                                                                </template>
                                                        </td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>

                                        <div class="slds-text-align_right slds-m-top_small">
                                            <template if:true={isEditMode}>
                                                    <lightning-button label="添加阶梯"
                                                                    variant="neutral"
                                                                    icon-name="utility:add"
                                                                    data-group-index={groupIndex}
                                                                    onclick={handleAddTier}></lightning-button>
                                            </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                           
                            
                            <!-- 共享阶梯金额折扣落区 - 保留UI，因为组合报价方式需要 -->
                            <template if:true={group.hasSharedLadderAmountDiscountDown}>
                                <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 10px; border-radius: 8px; height: 100%;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                            <div class="slds-text-heading_small slds-text-color_default">共享阶梯金额折扣</div>
                                        </div>

                                        <div class="slds-m-top_small">
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center">下限</th>
                                                        <th scope="col" class="slds-text-align_center">上限</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">单位</th> -->
                                                        <th scope="col" class="slds-text-align_center">折扣（%）</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">计算方式</th> -->
                                                        <template if:true={isEditMode}>
                                                        <th scope="col" class="slds-text-align_center">操作</th>
                                                        </template>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <template for:each={group.tiers} for:item="tier" for:index="tierIndex">
                                                    <tr key={tier.id}>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1"
                                                                                  value={tier.lowerBound}
                                                                              data-field="lowerBound"
                                                                              data-group-index={groupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.lowerBound}
                                                            </template>
                                                        </td>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1"
                                                                                  value={tier.upperBound}
                                                                              data-field="upperBound"
                                                                              data-group-index={groupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.upperBound}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.unit}
                                                                                  options={unitOptions}
                                                                                  data-field="unit"
                                                                                  data-group-index={groupIndex}
                                                                                  data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.unitNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.unit}
                                                            </template>
                                                        </td> -->
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="0.01"
                                                                                  value={tier.discount}
                                                                              data-field="discount"
                                                                              data-group-index={groupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.discount}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.calculationMethod}
                                                                                  options={calculationMethodOptions}
                                                                                  data-field="calculationMethod"
                                                                                  data-group-index={groupIndex}
                                                                                  data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.calculationMethodNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.calculationMethod}
                                                            </template>
                                                        </td> -->
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                    <template if:true={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="error"
                                                                                        size="small"
                                                                                        data-group-index={groupIndex}
                                                                                        data-tier-index={tierIndex}
                                                                                        onclick={handleDeleteTier}></lightning-button-icon>
                                                                    </template>
                                                                    <template if:false={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="border-filled"
                                                                                        size="small"
                                                                                        disabled="true"
                                                                                        data-group-index={groupIndex}
                                                                                        data-tier-index={tierIndex}></lightning-button-icon>
                                                                    </template>
                                                                </template>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>

                                    <div class="slds-text-align_right slds-m-top_small">
                                        <template if:true={isEditMode}>
                                                    <lightning-button label="添加阶梯"
                                                                    variant="neutral"
                                                                    icon-name="utility:add"
                                                                    data-group-index={groupIndex}
                                                                    onclick={handleAddTier}></lightning-button>
                                        </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            
                            <!-- 共享阶梯用量单价落区 -->
                            <template if:true={group.hasSharedLadderUsagePriceDown}>
                                <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 10px; border-radius: 8px; height: 100%;">
                                        <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                            <div class="slds-text-heading_small slds-text-color_default">阶梯用量单价</div>
                                        </div>
                                        <!-- 只有在非组合报价方式时才显示阶梯类型选择 -->
                                        <template if:true={currentConfigGroup}>
                                            <template if:false={currentConfigGroup.hasMinUnitPriceSharedLadderUsagePriceDown}>
                                                <template if:true={isEditMode}>
                                                        <lightning-combobox label="阶梯类型" value={currentConfigGroup.ladderType}
                                                        options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>

                                                </template>
                                                <template if:false={isEditMode}>
                                                        <lightning-combobox label="阶梯类型" disabled="true"  value={currentConfigGroup.ladderType}
                                                        options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>

                                                </template>
                                            </template>
                                        </template>
                                        <!-- 阶梯报价表格 -->
                                        <div class="slds-m-top_small">
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center">下限</th>
                                                        <th scope="col" class="slds-text-align_center">上限</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">单位</th> -->
                                                        <th scope="col" class="slds-text-align_center">单价（不含税）</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">计算方式</th> -->
                                                        <template if:true={isEditMode}>
                                                        <th scope="col" class="slds-text-align_center">操作</th>
                                                        </template>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <template for:each={group.tiers} for:item="tier" for:index="tierIndex">
                                                    <tr key={tier.id}>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1" 
                                                                                  value={tier.lowerBound}
                                                                              data-field="lowerBound" 
                                                                              data-group-index={groupIndex} 
                                                                              data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.lowerBound}
                                                            </template>
                                                        </td>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1" 
                                                                                  value={tier.upperBound}
                                                                              data-field="upperBound" 
                                                                              data-group-index={groupIndex} 
                                                                              data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.upperBound}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.unit}
                                                                                  options={unitOptions}
                                                                                  data-field="unit" 
                                                                                  data-group-index={groupIndex} 
                                                                                  data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.unitNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.unit}
                                                            </template>
                                                        </td> -->
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="0.000001"
                                                                                  value={tier.discount}
                                                                              data-field="discount"
                                                                              data-group-index={groupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  onblur={handleTierUnitPriceBlur}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.discount}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.calculationMethod}
                                                                                  options={calculationMethodOptions} 
                                                                                  data-field="calculationMethod" 
                                                                                  data-group-index={groupIndex} 
                                                                                  data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.calculationMethodNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.calculationMethod}
                                                            </template>
                                                        </td> -->
                                                          <template if:true={isEditMode}>
                                                            <td class="slds-text-align_center">
                                                                    <template if:true={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete" 
                                                                                        alternative-text="删除阶梯" 
                                                                                        variant="error" 
                                                                                        size="small"
                                                                                        data-group-index={groupIndex} 
                                                                                        data-tier-index={tierIndex} 
                                                                                        onclick={handleDeleteTier}></lightning-button-icon>
                                                                    </template>
                                                                    <template if:false={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete" 
                                                                                        alternative-text="删除阶梯" 
                                                                                        variant="border-filled" 
                                                                                        size="small"
                                                                                        disabled="true"
                                                                                        data-group-index={groupIndex} 
                                                                                        data-tier-index={tierIndex}></lightning-button-icon>
                                                                    </template>
                                                            </td>
                                                         </template>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                            
                                            <!-- 添加阶梯按钮 -->
                                        <div class="slds-text-align_right slds-m-top_small">
                                            <template if:true={isEditMode}>
                                                    <lightning-button label="添加阶梯" 
                                                                    variant="neutral" 
                                                                    icon-name="utility:add" 
                                                                    data-group-index={groupIndex}
                                                                    onclick={handleAddTier}></lightning-button>
                                        </template>
                                            </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        
                        <!-- 无报价方式时显示提示 -->
                        <template if:false={group.hasAnyQuoteType}>
                            <div class="slds-col slds-size_1-of-1">
                                <div class="slds-text-align_center slds-p-around_medium slds-text-color_weak">
                                    暂无报价方式，请点击"添加报价方式"按钮添加
                                </div>
                            </div>
                        </template>
                        </div>
                    </div>
                </div>
                    <!-- 没有产品时显示提示 -->
                    <template if:false={group.products.length}>
                        <div key={group.id} class="slds-text-align_center slds-p-around_medium slds-text-color_weak">
                            暂无产品数据
                        </div>
                    </template>
                    
                    <!-- 添加产品按钮 -->
                    <!-- <div style="display: flex; justify-content: flex-end; margin-top: 10px;">
                        <template if:true={isEditMode}>
                            <lightning-button label="添加产品" variant="neutral" icon-name="utility:add" 
                                            onclick={handleAddProduct} data-group-index={groupIndex}></lightning-button>
                        </template>
                    </div> -->
                </div>
        
            </div>
        </template>
        
        <!-- 没有产品组时显示提示 - 只在MAAS或AI Search产品线时显示 -->
        <template if:false={productGroups.length}>
            <template if:true={isMassProduct}>
                <div class="slds-box slds-theme_default slds-text-align_center slds-p-around_medium">
                    <div class="slds-text-color_weak">暂无产品组数据，请点击"添加产品组"按钮创建</div>
                </div>
            </template>
            <template if:true={isAISearchProduct}>
                <div class="slds-box slds-theme_default slds-text-align_center slds-p-around_medium">
                    <div class="slds-text-color_weak">暂无产品组数据，请点击"添加产品组"按钮创建</div>
                </div>
            </template>
        </template>
        
        
        <!-- <template if:true={isEditMode}>
            <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
                <lightning-button label="重置" variant="neutral" class="slds-m-right_x-small" onclick={handleReset} disabled={isSubmitting}></lightning-button>
                <lightning-button label="提交" variant="brand" onclick={handleSubmit} disabled={isSubmitting}>
                    <template if:true={isSubmitting}>
                        <lightning-spinner alternative-text="提交中" size="small" variant="brand"></lightning-spinner>
                    </template>
                </lightning-button>
            </div>
        </template> -->
    </template>

    <template if:true={isModalOpen}>
        <!-- Modal/Popup Box LWC starts here -->
        <section  role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="width: 121rem;max-width: 98rem;">
                <!-- Modal/Popup Box LWC header here -->
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeModal}>
                        <lightning-icon icon-name="utility:close"
                            alternative-text="close"
                            variant="inverse"
                            size="small" ></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">添加产品</h2>
                </header>
                <!-- Modal/Popup Box LWC body starts here -->
                <div class="slds-modal__content slds-p-around_medium" id="modal-content-id-1">

                        <c-product-info quote-name={QuoteName} quote-level={quoteLevel} record-id={recordId} onaddproduct={addProductMent} group-index={groupIndex} existing-products={currentExistingProducts}></c-product-info>

                </div>
                <!-- Modal/Popup Box LWC footer starts here -->
                <!-- <footer class="slds-modal__footer">
                    <button class="slds-button slds-button_neutral" onclick={closeModal} title="Cancel">取消</button>
                    <button class="slds-button slds-button_brand" onclick={submitDetails} title="OK">选择</button>
                </footer> -->
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- 配置报价弹窗 -->
    <template if:true={showQuoteConfigModal}>
        <section role="dialog" tabindex="-1" aria-modal="true" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container" style="width: 121rem;max-width: 98rem;">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeModal}>
                        <lightning-icon icon-name="utility:close"
                            alternative-text="close"
                            variant="inverse"
                            size="small" ></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-modal__title">配置{currentConfigGroupLabel}报价方式</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <!--报价方式选择 -->
                    <template if:true={isEditMode}>
                        <lightning-combobox label="报价方式"
                            value={currentConfigGroup.quoteTypeValue}
                            data-id={currentConfigGroupIndex}
                            options={quoteTypeOptions}
                            onchange={handleQuoteTypeChange}
                            disabled={currentConfigGroup.hasAnyQuoteType}>
                        </lightning-combobox>
                    </template>
                   
                    <div class="slds-box slds-theme_default">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <div class="slds-text-heading_small slds-text-color_default">{currentConfigGroupLabel}报价方式</div>
                            <template if:true={currentConfigGroup.hasAnyQuoteType}>
                                <div if:true={isEditMode}>
                                    <lightning-button-icon icon-name="utility:delete" alternative-text="删除报价方式" 
                                                        variant="border-filled" size="small" 
                                                        data-group-index={currentConfigGroupIndex}
                                                        onclick={handleDeleteAllQuoteTypes}></lightning-button-icon>
                                </div>
                            </template>
                        </div>
                        
                    <div class="slds-grid slds-wrap slds-gutters">
                        <!-- 保底金额卡片 -->
                        <template if:true={currentConfigGroup.hasMinimumAmountQuote}>
                                <div class="slds-col slds-size_1-of-3 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px;">
                                    <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <div class="slds-text-heading_small slds-text-color_default">保底金额</div>
                                    </div>
                                    
                                        <!-- 保底金额表单 - 表格样式 -->
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                    <th scope="col" class="slds-text-align_center">保底金额（不含税）</th>
                                                    <th scope="col" class="slds-text-align_center">保底类型</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="slds-text-align_center">
                                                <template if:true={isEditMode}>
                                                    <lightning-input type="number" step="0.01"
                                                                  value={currentConfigGroup.minimumAmount}
                                                                  data-field="minimumAmount"
                                                                  data-group-index={currentConfigGroupIndex}
                                                                  onchange={handleMinimumAmountChange}
                                                                          variant="label-hidden"
                                                                  required></lightning-input>
                                                </template>
                                                <template if:false={isEditMode}>
                                                    <div>{currentConfigGroup.minimumAmount}</div>
                                                </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-combobox
                                                                value={currentConfigGroup.minimumGuaranteeType}
                                                                options={minimumGuaranteeTypeOptions}
                                                                onchange={handleMinimumGuaranteeTypeChange}
                                                                variant="label-hidden">
                                                            </lightning-combobox>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{currentConfigGroup.minimumGuaranteeTypeLabel}</div>
                                                        </template>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                </div>
                            </div>
                        </template>
                        
                            <!-- 单价*数量卡片 -->
                            <template if:true={currentConfigGroup.hasMinimumUnitPriceQuote}>
                                <div class="slds-col slds-size_1-of-3 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px;">
                                    <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                            <div class="slds-text-heading_small slds-text-color_default">单价*数量</div>
                                    </div>
                                    
                                        <!-- 单价*数量表单 - 表格样式 -->
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                        <thead>
                                            <tr class="slds-line-height_reset">
                                                    <th scope="col" class="slds-text-align_center"><span style="color: red;">*</span>单价（不含税）</th>
                                                    <th scope="col" class="slds-text-align_center"><span style="color: red;">*</span>数量</th>
                                                    <th scope="col" class="slds-text-align_center">是否赠送</th>
                                                    <!-- <th scope="col" class="slds-text-align_center"><span style="color: red;">*</span>扩大倍数</th> -->
                                            </tr>
                                        </thead>
                                        <tbody>
                                                <tr>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                             <div style="display: flex; align-items: center; justify-content: center;">
                                                                <span style="color: red; margin-right: 4px;">*</span>
                                                                <lightning-input type="number" step="0.000001"
                                                                            value={currentConfigGroup.minimumUnitPrice}
                                                                            data-field="minimumUnitPrice"
                                                                            data-group-index={currentConfigGroupIndex}
                                                                            onchange={handleMinimumUnitPriceChange}
                                                                            onblur={handleUnitPriceBlur}
                                                                           
                                                                            variant="label-hidden"
                                                                            required></lightning-input>
                                                            </div>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{currentConfigGroup.minimumUnitPrice}</div>
                                                        </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                             <div style="display: flex; align-items: center; justify-content: center;">
                                                                <span style="color: red; margin-right: 4px;">*</span>
                                                                <lightning-input type="number" step="0.000001"
                                                                            value={currentConfigGroup.minimumQuantity}
                                                                            data-field="minimumQuantity"
                                                                            data-group-index={currentConfigGroupIndex}
                                                                            onchange={handleMinimumUnitPriceChange}
                                                                            
                                                                            variant="label-hidden"
                                                                            required></lightning-input>
                                                            </div>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{currentConfigGroup.minimumQuantity}</div>
                                                        </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-combobox
                                                                value={currentConfigGroup.isGift}
                                                                options={isGiftOptions}
                                                                onchange={handleIsGiftChange}
                                                                variant="label-hidden">
                                                            </lightning-combobox>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{currentConfigGroup.isGiftLabel}</div>
                                                        </template>
                                                    </td>
                                                    <!-- <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                             <div style="display: flex; align-items: center; justify-content: center;">
                                                                <span style="color: red; margin-right: 4px;">*</span>
                                                                <lightning-input type="number" step="1"
                                                                              value={currentConfigGroup.expandMultiple}
                                                                              data-field="expandMultiple"
                                                                              data-group-index={currentConfigGroupIndex}
                                                                              onchange={handleMinimumUnitPriceChange}
                                                                              variant="label-hidden"
                                                                              required
                                                                              min="1"
                                                                              max="99999"
                                                                              message-when-value-missing="扩大倍数为必填项"
                                                                              message-when-range-overflow="扩大倍数不能超过99999"
                                                                              message-when-range-underflow="扩大倍数不能小于1"></lightning-input>
                                                            </div>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{currentConfigGroup.expandMultiple}</div>
                                                        </template>
                                                    </td> -->
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                            </template>
                                            
                            <!-- 固定金额卡片 - 已注释掉 -->
                            <!-- <template if:true={currentConfigGroup.hasFixedAmountQuote}>
                                <div class="slds-col slds-size_1-of-3 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                            <div class="slds-text-heading_small slds-text-color_default">固定金额</div>
                                        </div>

                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                    <th scope="col" class="slds-text-align_center">固定单价</th>
                                                    <th scope="col" class="slds-text-align_center">固定数量</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-input type="number" step="0.01"
                                                                          value={currentConfigGroup.fixedAmount}
                                                                          data-field="fixedAmount"
                                                                          data-group-index={currentConfigGroupIndex}
                                                                          onchange={handleFixedUsageChange}
                                                                          variant="label-hidden"
                                                                          required></lightning-input>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{currentConfigGroup.fixedAmount}</div>
                                                        </template>
                                                    </td>
                                                    <td class="slds-text-align_center">
                                                        <template if:true={isEditMode}>
                                                            <lightning-input type="number" step="1"
                                                                          value={currentConfigGroup.fixedUsage}
                                                                          data-field="fixedUsage"
                                                                          data-group-index={currentConfigGroupIndex}
                                                                          onchange={handleFixedUsageChange}
                                                                          variant="label-hidden"
                                                                          required></lightning-input>
                                                        </template>
                                                        <template if:false={isEditMode}>
                                                            <div>{currentConfigGroup.fixedUsage}</div>
                                                        </template>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </template> -->
                            
                            <!-- 产品折扣卡片 -->
                            <template if:true={currentConfigGroup.hasProductDiscountQuote}>
                                <div class="slds-col slds-size_1-of-3 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 8px; border-radius: 4px;">
                                        <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                            <div class="slds-text-heading_small slds-text-color_default">产品折扣</div>
                                        </div>
                                        
                                        <!-- 产品折扣表单 - 分两行布局 -->
                                        <!-- 第一行：折扣系数和折扣类型 -->
                                        <template if:false={isMassProduct}>
                                            <!-- 非MAAS产品显示折扣系数和折扣类型 -->
                                            <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                                <thead>
                                                    <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center"><span style="color: red;">*</span>折扣系数（%）</th>
                                                        <th scope="col" class="slds-text-align_center">折扣类型</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <div style="display: flex; align-items: center; justify-content: center;">
                                                                    <span style="color: red; margin-right: 4px;">*</span>
                                                                    <lightning-input type="number" step="0.01"
                                                                                  value={currentConfigGroup.discountCoefficient}
                                                                                  data-field="discountCoefficient"
                                                                                      data-group-index={currentConfigGroupIndex}
                                                                                  onchange={handleProductDiscountChange}
                                                                                  variant="label-hidden"
                                                                                  max="100"
                                                                                    min="1"
                                                                                  required></lightning-input>
                                                                </div>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{currentConfigGroup.discountCoefficient}</div>
                                                            </template>
                                                        </td>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-combobox
                                                                    value={currentConfigGroup.discountType}
                                                                    options={discountTypeOptions}
                                                                    onchange={handleDiscountTypeChange}
                                                                    variant="label-hidden">
                                                                </lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{currentConfigGroup.discountTypeLabel}</div>
                                                            </template>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </template>

                                        <template if:true={isMassProduct}>
                                            <!-- MAAS产品只显示折扣系数 -->
                                            <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                                <thead>
                                                    <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center"><span style="color: red;">*</span>折扣系数（%）</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <div style="display: flex; align-items: center; justify-content: center;">
                                                                    <span style="color: red; margin-right: 4px;">*</span>
                                                                    <lightning-input type="number" step="0.01"
                                                                                  value={currentConfigGroup.discountCoefficient}
                                                                                  data-field="discountCoefficient"
                                                                                      data-group-index={currentConfigGroupIndex}
                                                                                  onchange={handleProductDiscountChange}
                                                                                  variant="label-hidden"
                                                                                  max="100"
                                                                                    min="1"
                                                                                  required></lightning-input>
                                                                </div>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{currentConfigGroup.discountCoefficient}</div>
                                                            </template>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </template>

                                        <!-- 第二行：固定返利、现金减免、Credit - 仅非MAAS产品显示 -->
                                        <template if:false={isMassProduct}>
                                            <table class="slds-table slds-table_bordered slds-table_cell-buffer slds-m-top_x-small">
                                                <thead>
                                                    <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center">固定返利（%）</th>
                                                        <th scope="col" class="slds-text-align_center">现金减免（%）</th>
                                                        <th scope="col" class="slds-text-align_center">Credit（%）</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={currentConfigGroup.fixedRebate}
                                                                              data-field="fixedRebate"
                                                                              data-group-index={currentConfigGroupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              max="99.99"
                                                                              min="0.00"
                                                                              ></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{currentConfigGroup.fixedRebate}</div>
                                                            </template>
                                                        </td>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={currentConfigGroup.cashReduce}
                                                                              data-field="cashReduce"
                                                                                  data-group-index={currentConfigGroupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              ></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{currentConfigGroup.cashReduce}</div>
                                                            </template>
                                                        </td>
                                                        <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                <lightning-input type="number" step="0.01"
                                                                              value={currentConfigGroup.credit}
                                                                              data-field="credit"
                                                                                 data-group-index={currentConfigGroupIndex}
                                                                              onchange={handleProductDiscountChange}
                                                                              variant="label-hidden"
                                                                              ></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                <div>{currentConfigGroup.credit}</div>
                                                            </template>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </template>
                                    </div>
                                </div>
                            </template>
                            
                            <!-- 共享阶梯金额折扣分区-->
                            <template if:true={currentConfigGroup.hasSharedLadderAmountDiscountZone}>
                                <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 10px; border-radius: 8px; height: 100%;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                            <div class="slds-text-heading_small slds-text-color_default">阶梯金额折扣</div>
                                        </div>
                                        <template if:true={isEditMode}>
                                                <lightning-combobox label="阶梯类型" value={currentConfigGroup.ladderType} 
                                                options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>
            
                                        </template>
                                        <template if:false={isEditMode}>
                                                <lightning-combobox label="阶梯类型" disabled="true"  value={currentConfigGroup.ladderType} 
                                                options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>
            
                                        </template>
                                       
                                        <template if:true={isEditMode}>
                                            <lightning-combobox label="折扣类型" value={currentConfigGroup.discountType}
                                            options={discountTypeOptions} onchange={handleDiscountTypeChange} ></lightning-combobox>

                                        </template>
                                        <template if:false={isEditMode}>
                                            <lightning-combobox label="折扣类型" disabled="true"  value={currentConfigGroup.discountType}
                                            options={discountTypeOptions} onchange={handleDiscountTypeChange} ></lightning-combobox>

                                        </template>
                                                           
                                      
                                        <div class="slds-m-top_small">
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center">下限</th>
                                                        <th scope="col" class="slds-text-align_center">上限</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">单位</th> -->
                                                        <th scope="col" class="slds-text-align_center">折扣（%）</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">计算方式</th> -->
                                                        <template if:true={isEditMode}>
                                                            <th scope="col" class="slds-text-align_center">操作</th>
                                                        </template>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <template for:each={currentConfigGroup.tiers} for:item="tier" for:index="tierIndex">
                                                    <tr key={tier.id}>
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-input type="number" step="1"
                                                                                    value={tier.lowerBound}
                                                                                data-field="lowerBound"
                                                                                data-group-index={currentConfigGroupIndex}
                                                                                data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"></lightning-input>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.lowerBound}
                                                                </template>
                                                            </td>
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-input type="number" step="1"
                                                                                    value={tier.upperBound}
                                                                                data-field="upperBound"
                                                                                data-group-index={currentConfigGroupIndex}
                                                                                data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"></lightning-input>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.upperBound}
                                                                </template>
                                                            </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.unit}
                                                                                  options={unitOptions}
                                                                                  data-field="unit"
                                                                                  data-group-index={currentConfigGroupIndex}
                                                                                  data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.unitNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.unit}
                                                            </template>
                                                        </td> -->
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-input type="number" step="0.01"
                                                                                    value={tier.discount}
                                                                                data-field="discount"
                                                                                data-group-index={currentConfigGroupIndex}
                                                                                data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"></lightning-input>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.discount}
                                                                </template>
                                                            </td>
                                                            <!-- <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                        <lightning-combobox
                                                                                    value={tier.calculationMethod}
                                                                                    options={calculationMethodOptions}
                                                                                    data-field="calculationMethod"
                                                                                    data-group-index={currentConfigGroupIndex}
                                                                                    data-tier-index={tierIndex}
                                                                                    onchange={handleTierChange}
                                                                                    variant="label-hidden"
                                                                                    disabled={tier.calculationMethodNotEditable}></lightning-combobox>
                                                                </template>
                                                                <template if:false={isEditMode}>
                                                                        {tier.calculationMethod}
                                                                </template>
                                                            </td> -->
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                    <template if:true={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="error"
                                                                                        size="small"
                                                                                        data-group-index={currentConfigGroupIndex}
                                                                                        data-tier-index={tierIndex}
                                                                                        onclick={handleDeleteTier}></lightning-button-icon>
                                                                    </template>
                                                                    <template if:false={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="border-filled"
                                                                                        size="small"
                                                                                        disabled="true"
                                                                                        data-group-index={currentConfigGroupIndex}
                                                                                        data-tier-index={tierIndex}></lightning-button-icon>
                                                                    </template>
                                                                </template>
                                                        </td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>

                                        <div class="slds-text-align_right slds-m-top_small">
                                            <template if:true={isEditMode}>
                                                    <lightning-button label="添加阶梯"
                                                                    variant="neutral"
                                                                    icon-name="utility:add"
                                                                    data-group-index={currentConfigGroupIndex}
                                                                    onclick={handleAddTier}></lightning-button>
                                            </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                           
                            
                            <!-- 共享阶梯金额折扣落区 - 保留UI，因为组合报价方式需要 -->
                            <template if:true={currentConfigGroup.hasSharedLadderAmountDiscountDown}>
                                <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 10px; border-radius: 8px; height: 100%;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                            <div class="slds-text-heading_small slds-text-color_default">共享阶梯金额折扣</div>
                                        </div>
                                        <!-- <div>
                                            <template if:true={isEditMode}>
                                                <lightning-combobox label="阶梯类型" value={currentConfigGroup.ladderType} 
                                                options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>
            
                                            </template>
                                             <template if:false={isEditMode}>
                                                <lightning-combobox label="阶梯类型" disabled="true" value={currentConfigGroup.ladderType} 
                                                options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>
            
                                            </template>
                                        </div> -->
                                        <div class="slds-m-top_small">
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center">下限</th>
                                                        <th scope="col" class="slds-text-align_center">上限</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">单位</th> -->
                                                        <th scope="col" class="slds-text-align_center">折扣（%）</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">计算方式</th> -->
                                                        <template if:true={isEditMode}>
                                                        <th scope="col" class="slds-text-align_center">操作</th>
                                                        </template>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <template for:each={currentConfigGroup.tiers} for:item="tier" for:index="tierIndex">
                                                    <tr key={tier.id}>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1"
                                                                                  value={tier.lowerBound}
                                                                              data-field="lowerBound"
                                                                              data-group-index={currentConfigGroupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.lowerBound}
                                                            </template>
                                                        </td>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1"
                                                                                  value={tier.upperBound}
                                                                              data-field="upperBound"
                                                                              data-group-index={currentConfigGroupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.upperBound}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.unit}
                                                                                  options={unitOptions}
                                                                                  data-field="unit"
                                                                                  data-group-index={currentConfigGroupIndex}
                                                                                  data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.unitNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.unit}
                                                            </template>
                                                        </td> -->
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="0.01"
                                                                                  value={tier.discount}
                                                                              data-field="discount"
                                                                              data-group-index={currentConfigGroupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.discount}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.calculationMethod}
                                                                                  options={calculationMethodOptions}
                                                                                  data-field="calculationMethod"
                                                                                  data-group-index={currentConfigGroupIndex}
                                                                                  data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.calculationMethodNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.calculationMethod}
                                                            </template>
                                                        </td> -->
                                                            <td class="slds-text-align_center">
                                                                <template if:true={isEditMode}>
                                                                    <template if:true={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="error"
                                                                                        size="small"
                                                                                        data-group-index={currentConfigGroupIndex}
                                                                                        data-tier-index={tierIndex}
                                                                                        onclick={handleDeleteTier}></lightning-button-icon>
                                                                    </template>
                                                                    <template if:false={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete"
                                                                                        alternative-text="删除阶梯"
                                                                                        variant="border-filled"
                                                                                        size="small"
                                                                                        disabled="true"
                                                                                        data-group-index={currentConfigGroupIndex}
                                                                                        data-tier-index={tierIndex}></lightning-button-icon>
                                                                    </template>
                                                                </template>
                                                        </td>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>

                                        <div class="slds-text-align_right slds-m-top_small">
                                            <template if:true={isEditMode}>
                                                    <lightning-button label="添加阶梯"
                                                                    variant="neutral"
                                                                    icon-name="utility:add"
                                                                    data-group-index={currentConfigGroupIndex}
                                                                    onclick={handleAddTier}></lightning-button>
                                            </template>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                            
                            <!-- 共享阶梯用量单价落区 -->
                            <template if:true={currentConfigGroup.hasSharedLadderUsagePriceDown}>
                                <div class="slds-col slds-size_1-of-1 slds-m-bottom_medium">
                                    <div style="border: 2px solid #d8dde6; padding: 10px; border-radius: 8px; height: 100%;">
                                        <!-- 报价方式标题 -->
                                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                            <div class="slds-text-heading_small slds-text-color_default">共享阶梯用量单价</div>
                                        </div>
                                        <!-- 只有在非组合报价方式时才显示阶梯类型选择 -->
                                        <template if:true={currentConfigGroup}>
                                            <template if:false={currentConfigGroup.hasMinUnitPriceSharedLadderUsagePriceDown}>
                                             <template if:true={isEditMode}>
                                                    <lightning-combobox label="阶梯类型" value={currentConfigGroup.ladderType}
                                                    options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>

                                            </template>
                                            <template if:false={isEditMode}>
                                                    <lightning-combobox label="阶梯类型" disabled="true"  value={currentConfigGroup.ladderType}
                                                    options={calculationMethodOptions} onchange={handleladderTypeChange} ></lightning-combobox>

                                            </template>
                                            </template>
                                        </template>
                                        <!-- 扩大倍数输入框 - 只在非组合报价方式时显示 -->
                                        <!-- <template if:false={currentConfigGroup.hasMinUnitPriceSharedLadderUsagePriceDown}>
                                            <div class="slds-form-element slds-m-bottom_small">
                                                <label class="slds-form-element__label" for="expand-multiple-config"><span style="color: red;">*</span>扩大倍数</label>
                                                <div class="slds-form-element__control">
                                                    <template if:true={isEditMode}>
                                                        <lightning-input type="number" step="1"
                                                                      value={currentConfigGroup.expandMultiple}
                                                                      data-field="expandMultiple"
                                                                      data-group-index={currentConfigGroupIndex}
                                                                      onchange={handleMinimumUnitPriceChange}
                                                                      variant="label-hidden"
                                                                      required
                                                                      min="1"
                                                                      max="99999"
                                                                      message-when-value-missing="扩大倍数为必填项"
                                                                      message-when-range-overflow="扩大倍数不能超过99999"
                                                                      message-when-range-underflow="扩大倍数不能小于1"></lightning-input>
                                                    </template>
                                                    <template if:false={isEditMode}>
                                                        <div class="slds-form-element__static">{currentConfigGroup.expandMultiple}</div>
                                                    </template>
                                                </div>
                                            </div>
                                        </template> -->
                                        <!-- 阶梯报价表格 -->
                                        <div class="slds-m-top_small">
                                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                                            <thead>
                                                <tr class="slds-line-height_reset">
                                                        <th scope="col" class="slds-text-align_center">下限</th>
                                                        <th scope="col" class="slds-text-align_center">上限</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">单位</th> -->
                                                        <th scope="col" class="slds-text-align_center">单价（不含税）</th>
                                                        <!-- <th scope="col" class="slds-text-align_center">计算方式</th> -->
                                                        <template if:true={isEditMode}>
                                                        <th scope="col" class="slds-text-align_center">操作</th>
                                                        </template>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <template for:each={currentConfigGroup.tiers} for:item="tier" for:index="tierIndex">
                                                    <tr key={tier.id}>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1" 
                                                                                  value={tier.lowerBound}
                                                                              data-field="lowerBound" 
                                                                              data-group-index={currentConfigGroupIndex} 
                                                                              data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.lowerBound}
                                                            </template>
                                                        </td>
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="1" 
                                                                                  value={tier.upperBound}
                                                                              data-field="upperBound" 
                                                                              data-group-index={currentConfigGroupIndex} 
                                                                              data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.upperBound}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.unit}
                                                                                  options={unitOptions}
                                                                                  data-field="unit" 
                                                                                  data-group-index={currentConfigGroupIndex} 
                                                                                  data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.unitNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.unit}
                                                            </template>
                                                        </td> -->
                                                            <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-input type="number" step="0.000001"
                                                                                  value={tier.discount}
                                                                              data-field="discount"
                                                                              data-group-index={currentConfigGroupIndex}
                                                                              data-tier-index={tierIndex}
                                                                                  onchange={handleTierChange}
                                                                                  onblur={handleTierUnitPriceBlur}
                                                                                  variant="label-hidden"></lightning-input>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.discount}
                                                            </template>
                                                        </td>
                                                            <!-- <td class="slds-text-align_center">
                                                            <template if:true={isEditMode}>
                                                                    <lightning-combobox
                                                                                  value={tier.calculationMethod}
                                                                                  options={calculationMethodOptions} 
                                                                                  data-field="calculationMethod" 
                                                                                  data-group-index={currentConfigGroupIndex} 
                                                                                  data-tier-index={tierIndex} 
                                                                                  onchange={handleTierChange}
                                                                                  variant="label-hidden"
                                                                                  disabled={tier.calculationMethodNotEditable}></lightning-combobox>
                                                            </template>
                                                            <template if:false={isEditMode}>
                                                                    {tier.calculationMethod}
                                                            </template>
                                                        </td> -->
                                                        <template if:true={isEditMode}>
                                                            <td class="slds-text-align_center">
                                                                     <template if:true={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete" 
                                                                                        alternative-text="删除阶梯" 
                                                                                        variant="error" 
                                                                                        size="small"
                                                                                        data-group-index={currentConfigGroupIndex} 
                                                                                        data-tier-index={tierIndex} 
                                                                                        onclick={handleDeleteTier}></lightning-button-icon>
                                                                    </template>
                                                                    <template if:false={tier.isDeletable}>
                                                                        <lightning-button-icon icon-name="utility:delete" 
                                                                                        alternative-text="删除阶梯" 
                                                                                        variant="border-filled" 
                                                                                        size="small"
                                                                                        disabled="true"
                                                                                        data-group-index={currentConfigGroupIndex} 
                                                                                        data-tier-index={tierIndex}></lightning-button-icon>
                                                                    </template>
                                                                </td>
                                                         </template>
                                                    </tr>
                                                </template>
                                            </tbody>
                                        </table>
                                            
                                            <!-- 添加阶梯按钮 -->
                                        <div class="slds-text-align_right slds-m-top_small">
                                            <template if:true={isEditMode}>
                                                    <lightning-button label="添加阶梯" 
                                                                    variant="neutral" 
                                                                    icon-name="utility:add" 
                                                                    data-group-index={currentConfigGroupIndex}
                                                                    onclick={handleAddTier}></lightning-button>
                                            </template>
                                            </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        
                        <!-- 无报价方式时显示提示 -->
                        <template if:false={currentConfigGroup.hasAnyQuoteType}>
                            <div class="slds-col slds-size_1-of-1">
                                <div class="slds-text-align_center slds-p-around_medium slds-text-color_weak">
                                    暂无报价方式，请点击"添加报价方式"按钮添加
                                </div>
                            </div>
                        </template>
                        </div>
                    </div>
                </div>
                <footer class="slds-modal__footer">
                    <lightning-button label="取消" onclick={closeQuoteConfigModal}></lightning-button>
                    <lightning-button label="保存" variant="brand" onclick={handleSaveQuoteConfig}></lightning-button>
                </footer>
            </div>
          
        </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
</template>

    <template if:true={showDeleteConfirmationModal}>
        <section role="dialog" tabindex="-1" aria-modal="true" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">
                        <lightning-icon icon-name="utility:warning" variant="warning" size="small"></lightning-icon>
                        确定删除当前三级目录{level3NameToDelete}的所有产品吗?
                    </h2>
                </header>
                <footer class="slds-modal__footer">
                    <lightning-button label="否" onclick={closeConfirmationModal}></lightning-button>
                    <lightning-button label="是" variant="brand" onclick={handleConfirmDeletion}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- 更多标签弹窗 -->
    <template if:true={showMoreTagsModal}>
        <section role="dialog" tabindex="-1" aria-modal="true" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">
                        <lightning-icon icon-name="utility:list" variant="info" size="small"></lightning-icon>
                        所有三级产品标签
                    </h2>
                    <lightning-button-icon
                        icon-name="utility:close"
                        onclick={closeMoreTagsModal}
                        alternative-text="关闭"
                        variant="bare-inverse"
                        class="slds-modal__close">
                    </lightning-button-icon>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                        <template if:true={currentGroupForMoreTags}>
                            <template for:each={currentGroupForMoreTags.allLevelThreeProducts} for:item="level3Product">
                                <lightning-pill key={level3Product.id}
                                                label={level3Product.name}
                                                onremove={handleRemoveLevelThreeFromModal}
                                                data-level3-name={level3Product.name}>
                                </lightning-pill>
                            </template>
                        </template>
                    </div>
                </div>
                <footer class="slds-modal__footer">
                    <lightning-button label="关闭" onclick={closeMoreTagsModal}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>

    <!-- 客户账号ID配置模态框 -->
    <template if:true={showCustomerIdsModal}>
        <section role="dialog" tabindex="-1" aria-modal="true" class="slds-modal slds-fade-in-open">
            <c-customer-ids-config
                customer-account-id={currentCustomerAccountId}
                product-id={currentProductId}
                onsave={handleCustomerIdsSave}
                oncancel={handleCustomerIdsCancel}>
            </c-customer-ids-config>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>