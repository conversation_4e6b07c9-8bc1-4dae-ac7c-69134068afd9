public without sharing class Interface_CRMSyncQuoteToSAP {
    public static Interface_OutboundParam param {get;set;}

    @future(callout=true)
    public static void doSyncQuoteToSAP(String quoteId) {
        param = new Interface_OutboundParam();
        param.interfaceName = 'CRMSyncQuoteToSAP';
        param.endpoint ='callout:SAP_API_Credential'; //改动
        param.retryTimes = 3;
        param.recordIdSet = new Set<id>{quoteId};
        param.targetObject = 'Quote';
        // param.httpMethod = 'POST';
        // String token = '';
        param.functionDescription='同步报价单信息到SAP';
        param.reActionScript = 'Interface_CRMSyncQuoteToSAP.doSyncQuoteToSAP('+quoteId+');';
        param.requestHeader = getRequestHeader();
        param.dataString = getRequestBody(quoteId);      

        Interface_OutboundExecutor syncContract = new Interface_OutboundExecutor(param);
        String body = syncContract.execute();
        System.debug('body'+body);
        
    }

    public static String getRequestHeader(){
        // String encodedCredentials = CWUtility.getSAPEncodedCredentials();
        Map<String, String> headerMap = new Map<String, String>();
        headerMap.put('Content-Type', 'application/json');
        // headerMap.put('sap-client', '110'); //改动
        //headerMap.put('Authorization', 'Bearer '+token);
        //headerMap.put('Authorization', 'Basic ' + encodedCredentials);
        List<String> headerList = new List<String>();
        for (String key : headerMap.keySet()) {
            headerList.add(key + '=' + headerMap.get(key));
        }
        String headerString = String.join(headerList, ';');
        return headerString;
    }

    public static String getRequestBody(String quoteId){
        QuoteWrpper wrapper = new QuoteWrpper();
        wrapper.req = new Req();
        Quote qObj = [SELECT Id,QuoteNumber,Account.SAP_Num__c,
                        PartyB_Signing_Company__r.Customer_SimpleName__c , PartyB_Signing_Company__c 
                      FROM Quote 
                      WHERE Id =:quoteId];
        wrapper.req.ZQUNUM = qObj.QuoteNumber;
        wrapper.req.VKORG = qObj.PartyB_Signing_Company__r.Customer_SimpleName__c;
        wrapper.req.VTWEG = 'D1';
        wrapper.req.KUNNR = qObj.Account.SAP_Num__c;
        wrapper.req.Item = new List<QuoteLineObj>();
        Interface_CRMSyncQuoteToSAP.getGroupWrapper(quoteId,wrapper);
        Interface_CRMSyncQuoteToSAP.getsingleWrapper(quoteId,wrapper);
        System.debug('wrapper****:' + JSON.serialize(wrapper, true));
        return JSON.serialize(wrapper, true);
    }

    public static QuoteWrpper getGroupWrapper(String quoteId,QuoteWrpper wrapper) {
        // 获取报价单下的产品组
        List<QuoteLineItem> quoteLineItems = Database.query(
            'SELECT Id,Quote.Contract_Cur__c,Quote.Settlement_Cur__c,IsExchangeRateDifference__c,ListPrice__c, Product2Id, Product2.Name, Product2.ProductCode, toLabel(Product2.Family), ' +
            'Product2.Description,CurrencyIsoCode, Product2.QuantityUnitOfMeasure, UnitPrice, ' +
            'Tax_Rate__c, Account_ID__c, Profit_Statement__c, Description, ' +
            'Pricing__c, Pricing__r.Unit__c, Pricing__r.Amount__c,Pricing__r.Price_Unit__c,Pricing__r.CurrencyIsoCode, ' +
            'QuoteLineStartDate__c, QuoteLineEndDate__c, Region__c,IS_OneTimeFee__c,Charge_Type__c, ' +
            'Product_Group__c, Product_Group__r.QuotationMethod_Ladder__c, ' +
            'Product_Group__r.QuotationMethod_Ladder__r.Method__c, ISGROUP__c, ' +
            'Product2.Level__c, Product2.ParentProduct__r.Name, Product2.ParentProduct__r.ParentProduct__r.Name, ' +
            'Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name,Quote.Product_Cate__c  ' +
            'FROM QuoteLineItem ' +
            'WHERE QuoteId = :quoteId AND Product_Group__c != null AND ISGROUP__c = true ' +
            'ORDER BY CreatedDate ASC'
        );
        
        // 收集所有产品ID，用于查询产品牌价
        Set<Id> quoteLineIds = new Set<Id>();
        List<Id> productIds = new List<Id>();
        Set<Id> ladderDiscountIds = new Set<Id>();
        Map<String,List<QuoteLineItem>> groupMap = new Map<String,List<QuoteLineItem>>();
        for (QuoteLineItem qli : quoteLineItems) {
            productIds.add(qli.Product2Id);
            quoteLineIds.add(qli.Id);
            ladderDiscountIds.add(qli.Product_Group__c);
            if(groupMap.containsKey(qli.Product_Group__c)){
                groupMap.get(qli.Product_Group__c).add(qli);
            }else{
                List<QuoteLineItem> qliList = new List<QuoteLineItem>();
                qliList.add(qli);
                groupMap.put(qli.Product_Group__c, qliList);
            }
        }
        // 查询产品牌价对象
        Map<String, ProductPrice__c> priceInfo = new Map<String, ProductPrice__c>();
        for (ProductPrice__c price : [
            SELECT Id, Product__c, Amount__c,Unit__c, CurrencyIsoCode
            FROM ProductPrice__c
            WHERE Product__c IN :productIds
        ]) {
            priceInfo.put(price.Product__c, price);   
        }
        
        // 查询所有报价方式数据
        Map<Id, Quotation_Method__c> groupQuotationMethods = new Map<Id, Quotation_Method__c>();
        
        // 保底金额+共享阶梯金额折扣落区组合报价方式
        Map<Id, Quotation_Method__c> groupMinimumAmountQuotations = new Map<Id, Quotation_Method__c>();
        List<Quotation_Method__c> minimumAmountQuotations = [
                SELECT Id,Product_Group__c,GuaranteedMin_Amount__c,Minimum_ProdUnitPrice__c, Method__c,Minimum_Amout__c,Minimum_Price__c, 
                Discount_Factor__c,LadderType__c,KPEIN__c,IsGift__c, Fixed_Rebate__c, Cash_Reduce__c, Credit__c,MinimumGuarantee_type__c,Discount_Type__c
                FROM Quotation_Method__c
                WHERE Product_Group__c IN:ladderDiscountIds
                AND ISGROUP__c = true
        ];
        for(Quotation_Method__c qm : minimumAmountQuotations) {
            groupMinimumAmountQuotations.put(qm.Product_Group__c, qm);
        
        }
        // 获取所有相关的阶梯报价行
        Map<Id, List<Ladder_Line__c>> ladderLinesByLadderDiscount = new Map<Id, List<Ladder_Line__c>>();
        if (!ladderDiscountIds.isEmpty()) {
            for (Ladder_Line__c line : [
                SELECT Id,Name, Product_Group__c, Up_Limit__c, Down_Limit__c, 
                        Unit__c, Discount__c, Calculation_Method__c
                FROM Ladder_Line__c
                WHERE Product_Group__c IN :ladderDiscountIds
                
            ]) {
                if (!ladderLinesByLadderDiscount.containsKey(line.Product_Group__c)) {
                    ladderLinesByLadderDiscount.put(line.Product_Group__c, new List<Ladder_Line__c>());
                }
                ladderLinesByLadderDiscount.get(line.Product_Group__c).add(line);
            }
        }
        for(String pg : groupMap.keySet()){
            List<QuoteLineItem> qliList = groupMap.get(pg);
            Quotation_Method__c qm = groupMinimumAmountQuotations.get(pg);
            for(QuoteLineItem qli : qliList){
                QuoteLineObj lineObj = new QuoteLineObj();
                lineObj.ZQUOTYPE = qm.Method__c;
                lineObj.ZAREA = qli.Region__c;
                lineObj.MATNR = qli.Product2.ProductCode;

                //如果是maas和AIsearch产品线就传产品的牌价币种
                if(qli.Pricing__r != null){
                    lineObj.KONWA = qli.Pricing__r?.CurrencyIsoCode;
                }else{
                     lineObj.KONWA = qli.Quote.Settlement_Cur__c;
                }    

                
                if((qm.Method__c == '1' || qm.Method__c == '6')){
                    if(qm.Minimum_ProdUnitPrice__c != null && qm.Minimum_ProdUnitPrice__c.scale() > 2){
                        Integer decimalPlaces = qm.Minimum_ProdUnitPrice__c.scale() - 2;
                        lineObj.KPEIN = Math.pow(10, decimalPlaces);
                    }else{
                        lineObj.KPEIN = 1;
                    }
                    lineObj.KBETR = qm.Minimum_ProdUnitPrice__c * lineObj.KPEIN;
                }else if(qli.Pricing__r != null) {
                    lineObj.KPEIN = qli.Pricing__r.Price_Unit__c;
                    lineObj.KBETR = qm.Minimum_ProdUnitPrice__c * lineObj.KPEIN;
                } 
                else {
                    lineObj.KPEIN = 1;
                    lineObj.KBETR = qm.Minimum_ProdUnitPrice__c;
                }
               
                if(qli.Pricing__r != null){
                    lineObj.KMEIN = qli.Pricing__r?.Unit__c;
                }else{
                    lineObj.KMEIN = qli.Product2.QuantityUnitOfMeasure;
                }

                lineObj.ZEXCRD = qli.IsExchangeRateDifference__c;
                if(qli.Pricing__r != null){
                    lineObj.ZBPRICE = (qli.Pricing__r?.Amount__c==null?0:qli.Pricing__r?.Amount__c) * qli.unitPrice;
                }else{
                    lineObj.ZBPRICE = 1;
                }
                lineObj.ZHLCXS = qli.unitPrice;
                lineObj.ZSFZS = qm.IsGift__c=='1'?'X':''; 
                lineObj.ZQUANT = qm.Minimum_Amout__c;
                lineObj.ZGXZHBS ='X';
                lineObj.ZSHGRO = pg;
                lineObj.ZTRDTY = qm.LadderType__c;
                lineObj.ZBADTY = qm.MinimumGuarantee_type__c;
                //设置保底金额GuaranteedMin_Amount__c
                lineObj.ZBADPR = qm.GuaranteedMin_Amount__c;
                               
                lineObj.ZZKLX = qm.Discount_Type__c;   //设置折扣类型
                lineObj.ZQUITM = qli.Id;//设置报价行id
                lineObj.ZJSLX = qli.Charge_Type__c;
                if(qm.Discount_Type__c == '2'){
                    lineObj.ZDISCOUNT = (qm.Discount_Factor__c==null?0:qm.Discount_Factor__c);
                }else{
                    lineObj.ZDISCOUNT = 100 - (qm.Discount_Factor__c==null?0:qm.Discount_Factor__c);
                }
                
                lineObj.ZOTAMT =qli.IS_OneTimeFee__c=='是'?'X':'';
                lineObj.DATAB = dateFormat(qli.QuoteLineStartDate__c);
                lineObj.DATBI = dateFormat(qli.QuoteLineEndDate__c);
                lineObj.ZZGDFD = qm.Fixed_Rebate__c;
                lineObj.ZVOUCH = qm.Credit__c;
                lineObj.ZZCASH = qm.Cash_Reduce__c;
                lineObj.Item1 = new List<LadderObj>();
                if(ladderLinesByLadderDiscount.containsKey(pg) && ladderLinesByLadderDiscount.get(pg).size()>0){
                    for(Ladder_Line__c line: ladderLinesByLadderDiscount.get(pg)){
                        LadderObj lobj = new LadderObj();
                        if(qm.Method__c == '3' || qm.Method__c == '6'){
                            if(line.Discount__c != null && line.Discount__c.scale() > 2){
                                Integer decimalPlaces = line.Discount__c.scale() - 2;
                                lobj.KPEIN = Math.pow(10, decimalPlaces);
                            }else{
                                lobj.KPEIN = 1;
                            }
                            if(lineObj.KPEIN < lobj.KPEIN){
                                lineObj.KPEIN = lobj.KPEIN;
                            }
                        }
                    }
                    if(qm.Method__c == '1' || qm.Method__c == '6'){
                        lineObj.KBETR = qm.Minimum_ProdUnitPrice__c * lineObj.KPEIN;
                    }
                    for(Ladder_Line__c line: ladderLinesByLadderDiscount.get(pg)){
                        LadderObj lobj = new LadderObj();
                        lobj.KLFN1 = line.Name.right(4);
                        lobj.KSTBM = line.Down_Limit__c;
                        if(qm.Method__c == '4' || qm.Method__c == '5'){
                            if(qm.Discount_Type__c == '2'){
                                lobj.KBETR = line.Discount__c; 
                            }else{
                                lobj.KBETR = 100 - line.Discount__c; 
                            }
                            
                        }else{
                            // if(line.Discount__c != null && line.Discount__c.scale() > 2){
                            //     Integer decimalPlaces = line.Discount__c.scale() - 2;
                            //     lobj.KPEIN = Math.pow(10, decimalPlaces);
                            // }else{
                            //     lobj.KPEIN = 1;
                            // }
                            
                            lobj.KBETR = line.Discount__c * lineObj.KPEIN; 
                        }
                        lineObj.Item1.add(lobj);
                    }
                }
                

                
                wrapper.req.Item.add(lineObj);
            }
        }
        return wrapper;
    }

    public static QuoteWrpper getsingleWrapper(String quoteId,QuoteWrpper wrapper) {
        List<QuoteLineItem> quoteLineItems = Database.query(
                'SELECT Id,Quote.Contract_Cur__c,Quote.Settlement_Cur__c,Quote.Product_Cate__c,IsExchangeRateDifference__c, Product2Id, Product2.Name, Product2.ProductCode, toLabel(Product2.Family), ' +
                'Product2.Description,ListPrice__c, Product2.QuantityUnitOfMeasure, UnitPrice, ' +
                'Tax_Rate__c,CurrencyIsoCode, Account_ID__c, Profit_Statement__c, Description,Charge_Type__c, ' +
                'QuoteLineStartDate__c, QuoteLineEndDate__c, Region__c, ' +
                'Product_Group__c, IS_OneTimeFee__c,' +
                'Pricing__c, Pricing__r.Unit__c, Pricing__r.Amount__c,Pricing__r.Price_Unit__c,Pricing__r.CurrencyIsoCode, ' +
                'Product2.Level__c, Product2.ParentProduct__r.Name, Product2.ParentProduct__r.ParentProduct__r.Name, ' +
                'Product2.ParentProduct__r.ParentProduct__r.ParentProduct__r.Name ' +
                'FROM QuoteLineItem ' +
                'WHERE QuoteId = :quoteId AND ISGROUP__c = false AND Product_Group__c = null AND Product2Id != null ' +
                'ORDER BY CreatedDate ASC'
            );
            // 收集所有产品ID，用于查询产品牌价
            Set<Id> productIds = new Set<Id>();
            Set<Id> qliList = new Set<Id>();
            for (QuoteLineItem qli : quoteLineItems) {
                if (qli.Product2Id != null) {
                    productIds.add(qli.Product2Id);
                    qliList.add(qli.Id);
                }
            }
            // 查询产品牌价对象
            Map<String, ProductPrice__c> priceInfo = new Map<String, ProductPrice__c>();

            for (ProductPrice__c price : [
                SELECT Id, Product__c,Unit__c, Amount__c, CurrencyIsoCode
                FROM ProductPrice__c
                WHERE Product__c IN :productIds
            ]) {
                priceInfo.put(price.Product__c, price);
            }
             Map<String,Quotation_Method__c> qmMap = new Map<String,Quotation_Method__c>();  
            for (Quotation_Method__c qm : [
                SELECT Id,GuaranteedMin_Amount__c,Minimum_ProdUnitPrice__c,Quote_Line_Item_ID__c, Method__c,Minimum_Price__c,Minimum_Amout__c, Discount_Factor__c,
                LadderType__c, Fixed_Rebate__c, Cash_Reduce__c,KPEIN__c,IsGift__c, Credit__c,MinimumGuarantee_type__c,Discount_Type__c
                FROM Quotation_Method__c
                WHERE Quote_Line_Item_ID__c IN :qliList AND ISGROUP__c = false
            ]) {
                qmMap.put(qm.Quote_Line_Item_ID__c, qm);
            }
            // 获取阶梯行数据
            Map<Id, List<Ladder_Line__c>> ladderLinesByLadder = new Map<Id, List<Ladder_Line__c>>();
            List<Ladder_Line__c> ladderLines = [
                SELECT Id,Name, Down_Limit__c,QuoteLineItem__c, Up_Limit__c, Unit__c, Discount__c, Calculation_Method__c
                FROM Ladder_Line__c
                WHERE QuoteLineItem__c IN: qliList
                ORDER BY Down_Limit__c ASC
            ];
            for(Ladder_Line__c ln : ladderLines) {
                if (!ladderLinesByLadder.containsKey(ln.QuoteLineItem__c)) {
                    ladderLinesByLadder.put(ln.QuoteLineItem__c, new List<Ladder_Line__c>());
                }
                ladderLinesByLadder.get(ln.QuoteLineItem__c).add(ln);
            }
            for(QuoteLineItem qli : quoteLineItems){
                Quotation_Method__c qm = qmMap.get(qli.Id);
                QuoteLineObj lineObj = new QuoteLineObj();
                lineObj.ZQUOTYPE = qm.Method__c;
                lineObj.ZAREA = qli.Region__c;
                lineObj.MATNR = qli.Product2.ProductCode;
                
                //如果是maas和AIsearch产品线就传产品的牌价币种
                if(qli.Pricing__r != null){
                    lineObj.KONWA = qli.Pricing__r?.CurrencyIsoCode;
                }else{
                    lineObj.KONWA = qli.Quote.Settlement_Cur__c;
                }    

                if((qm.Method__c == '1' || qm.Method__c == '6')){
                    if(qm.Minimum_ProdUnitPrice__c != null && qm.Minimum_ProdUnitPrice__c.scale() > 2){
                        Integer decimalPlaces = qm.Minimum_ProdUnitPrice__c.scale() - 2;
                        lineObj.KPEIN = Math.pow(10, decimalPlaces);
                    }else{
                        lineObj.KPEIN = 1;
                    }
                    lineObj.KBETR = qm.Minimum_ProdUnitPrice__c * lineObj.KPEIN;
                }else if(qli.Pricing__r != null) {
                    lineObj.KPEIN = qli.Pricing__r.Price_Unit__c;
                    lineObj.KBETR = qm.Minimum_ProdUnitPrice__c * lineObj.KPEIN;
                } 
                else {
                    lineObj.KPEIN = 1;
                    lineObj.KBETR = qm.Minimum_ProdUnitPrice__c;
                }
               
                if(qli.Pricing__r != null){
                      lineObj.KMEIN = qli.Pricing__r.Unit__c;
                }else{
                    lineObj.KMEIN = qli.Product2.QuantityUnitOfMeasure;
                }

                lineObj.ZEXCRD = qli.IsExchangeRateDifference__c;
                System.debug('*************Pricing__r'+qli.Pricing__r);
                if(qli.Pricing__r != null){
                    lineObj.ZBPRICE = (qli.Pricing__r?.Amount__c==null?0:qli.Pricing__r?.Amount__c) * qli.unitPrice;
                    System.debug('*************牌价'+lineObj.ZBPRICE);
                }else{
                    lineObj.ZBPRICE = 1;
                }
                lineObj.ZHLCXS = qli.unitPrice; 
                lineObj.ZSFZS = qm.IsGift__c=='1'?'X':''; 
                lineObj.ZQUANT = qm.Minimum_Amout__c;
                // lineObj.ZGXZHBS ='X';
                // lineObj.ZSHGRO = pg;
                lineObj.ZTRDTY = qm.LadderType__c;
                lineObj.ZBADTY = qm.MinimumGuarantee_type__c;
                //设置保底金额
                lineObj.ZBADPR = qm.GuaranteedMin_Amount__c;
                // lineObj.ZBADTY = 
                lineObj.ZZKLX = qm.Discount_Type__c;   //设置折扣类型
                lineObj.ZQUITM = qli.Id;//设置报价行id
                lineObj.ZJSLX = qli.Charge_Type__c;
                if(qm.Discount_Type__c == '2'){
                    lineObj.ZDISCOUNT = (qm.Discount_Factor__c==null?0:qm.Discount_Factor__c);
                }else{
                    lineObj.ZDISCOUNT = 100 - (qm.Discount_Factor__c==null?0:qm.Discount_Factor__c);
                }
                lineObj.ZOTAMT =qli.IS_OneTimeFee__c=='是'?'X':'';
                lineObj.DATAB = dateFormat(qli.QuoteLineStartDate__c);
                lineObj.DATBI = dateFormat(qli.QuoteLineEndDate__c);
                lineObj.ZZGDFD = qm.Fixed_Rebate__c;
                lineObj.ZVOUCH = qm.Credit__c;
                lineObj.ZZCASH = qm.Cash_Reduce__c;
                lineObj.Item1 = new List<LadderObj>();
                if(ladderLinesByLadder.containsKey(qli.Id) && ladderLinesByLadder.get(qli.Id).size() >0){
                    for(Ladder_Line__c line: ladderLinesByLadder.get(qli.Id)){
                        LadderObj lobj = new LadderObj();
                        if(qm.Method__c == '3' || qm.Method__c == '6'){
                            if(line.Discount__c != null && line.Discount__c.scale() > 2){
                                Integer decimalPlaces = line.Discount__c.scale() - 2;
                                lobj.KPEIN = Math.pow(10, decimalPlaces);
                            }else{
                                lobj.KPEIN = 1;
                            }
                            if(lineObj.KPEIN < lobj.KPEIN){
                                lineObj.KPEIN = lobj.KPEIN;
                            }
                        }
                    }
                    if(qm.Method__c == '1' || qm.Method__c == '6'){
                        lineObj.KBETR = qm.Minimum_ProdUnitPrice__c * lineObj.KPEIN;
                    }
                    for(Ladder_Line__c line: ladderLinesByLadder.get(qli.Id)){
                        System.debug('line'+line);
                        LadderObj lobj = new LadderObj();
                        lobj.KLFN1 = line.Name.right(4);
                        lobj.KSTBM = line.Down_Limit__c;
                        if(qm.Method__c == '4' || qm.Method__c == '5'){
                            if(qm.Discount_Type__c == '2'){
                                lobj.KBETR = line.Discount__c; 
                            }else{
                                lobj.KBETR = 100 - line.Discount__c; 
                            }
                        }else{
                            // if(line.Discount__c != null && line.Discount__c.scale() > 2){
                            //     Integer decimalPlaces = line.Discount__c.scale() - 2;
                            //     lobj.KPEIN = Math.pow(10, decimalPlaces);
                            // }else{
                            //     lobj.KPEIN = 1;
                            // }
                            lobj.KBETR = line.Discount__c * lineObj.KPEIN; 
                            // lobj.KPEIN = qm.KPEIN__c;
                        }
                        lineObj.Item1.add(lobj);
                    }
                }
                
                wrapper.req.Item.add(lineObj);
            }
            return wrapper;
    }
       

    public static String dateFormat(Date d){
        String str = '';
        if(d != null){
            str = DateTime.newInstance(d.year(), d.month(), d.day()).format('yyyyMMdd');
        }
        return str;
    }

    public class QuoteWrpper{
        public String uuid;
        public String znumb;//接口编号
        public String fsysid;//请求系统
        public Req req {get; set;}//请求体
        public QuoteWrpper() {
            uuid = CWUtility.generateUUID();
            znumb = 'SD013';
            fsysid = 'SALESFORCE';
        }   
    }
    public class Req{
        public String ZQUNUM {get; set;} 
        public String VKORG {get; set;}  
        public String VTWEG {get; set;}  
        public String KUNNR {get; set;}  
   
        public List<QuoteLineObj> Item {get; set;}  
        
    }

    public class QuoteLineObj{
        public String ZQUOTYPE {get; set;}
        public String ZAREA {get; set;}
        public String MATNR {get; set;}
        public Decimal KBETR {get; set;}
        public String KONWA {get; set;}
        public Decimal KPEIN {get; set;}
        public String KMEIN {get; set;}
        public String ZEXCRD {get; set;}
        public String ZGXZHBS {get; set;}
        public Decimal ZBPRICE {get; set;}
        public Decimal ZQUANT {get; set;}
        public String ZTRDTY {get; set;}
        public Decimal ZBADPR {get; set;}
        public String ZBADTY {get; set;}//保底类型
        public Decimal ZDISCOUNT {get; set;}
        public String ZOTAMT {get; set;}
        public String DATAB {get; set;}
        public String ZSHGRO {get; set;}
        public String DATBI {get; set;}
        public Decimal ZZGDFD {get; set;}
        public Decimal ZVOUCH {get; set;}
        public Decimal ZZCASH {get; set;}
        public String ZJSLX {get; set;}
        public String ZQUITM {get; set;}//报价行id
        public String ZZKLX {get; set;}//折扣类型
        public Decimal ZHLCXS {get; set;}//汇率差系数 
        public String ZSFZS {get; set;}//是否赠送

        public List<LadderObj> Item1 {get; set;}
    }

    public class LadderObj{
        public String KLFN1 {get; set;} 
        public Decimal KSTBM {get; set;} 
        public Decimal KBETR {get; set;} 
        public Decimal KPEIN {get; set;} 
    }
}