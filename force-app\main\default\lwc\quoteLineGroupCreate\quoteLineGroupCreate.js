import { LightningElement, api, track, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { CurrentPageReference } from 'lightning/navigation';
import { NavigationMixin } from 'lightning/navigation';
import saveProductGroupQuote from '@salesforce/apex/QuoteLineGroupController.saveProductGroupQuote';
import saveSingleProductQuote from '@salesforce/apex/QuoteLineGroupController.saveSingleProductQuote';
import getProductGroups from '@salesforce/apex/QuoteLineGroupController.getProductGroups';
import getSingleProducts from '@salesforce/apex/QuoteLineGroupController.getSingleProducts';
import getProductAndAreaListPrice from '@salesforce/apex/QuoteLineGroupController.getProductAndAreaListPrice';
import handleProductInfoProducts from '@salesforce/apex/QuoteLineGroupController.handleProductInfoProducts';
import handleProductInfoProductsTemp from '@salesforce/apex/QuoteLineGroupController.handleProductInfoProductsTemp';
import getQuoteInfo from '@salesforce/apex/QuoteLineGroupController.getQuoteInfo';
import deleteProductsByLevelThreeName from '@salesforce/apex/QuoteLineGroupController.deleteProductsByLevelThreeName';
import calculateAndUpdateContractRevenue from '@salesforce/apex/QuoteLineGroupController.calculateAndUpdateContractRevenue';
import getPicklistValues  from '@salesforce/apex/QuoteLineGroupController.getPicklistValues';

export default class QuoteLineGroupCreate extends NavigationMixin(LightningElement) {
    @api recordId; // 报价单ID
    @api startInEditMode = false; // 是否在加载时自动进入编辑模式
    @track isLoading = false;
    @track isEditMode = false;
    @track isSubmitting = false;
    @track productGroups = []; // 产品组列表
    @track showQuoteTypeModal = false; // 报价方式选择模态框显示状态
    @track currentGroupIndex = null; // 当前操作的产品组索引
    @track selectedRows = {}; // 存储每个产品组中选中的行 - 键为groupIndex，值为选中行的数组
    @track selectedQuoteType = null; // 当前选择的报价方式
    @track groupIndex=null;
    @track isModalOpen = false; // 控制添加产品模态框显示状态
    @track showDeleteConfirmationModal = false; // 控制删除三级产品弹窗
    @track showMoreTagsModal = false; // 控制"更多"标签弹窗
    @track currentGroupForMoreTags = null; // 当前显示"更多"标签的产品组
    @track groupIndexForDeletion = null;
    @track level3NameToDelete = '';
    @track QuoteName = null
    @track quoteInfo = null; // 存储报价单详细信息
    @track unitNotEditable = false;
    @track calculationMethodNotEditable = false;
    // 单产品相关属性
    @track singleProducts = []; // 单产品列表
    @track selectedSingleProducts = []; // 选中的单产品
    @track singleProductTags = {}; // 单产品的三级产品标签
    // 临时产品管理
    @track tempProducts = []; // 临时产品列表，用于存储未保存的产品
    @track tempSingleProducts = []; // 临时单产品列表
    @track exchangeRateDifference='';
    @track draftValues= [];
    @track quoteLevel='';
    // 判断是否为MASS产品报价
    get isMassProduct() {
        const isMass = this.quoteInfo && this.quoteInfo.level === 'MAAS';
        console.log('isMassProduct check:', {
            quoteInfo: this.quoteInfo,
            level: this.quoteInfo?.level,
            isMass: isMass
        });
        return isMass;
    }

    // 判断是否为MSP产品报价
    get isMspProduct() {
        const isMsp = this.quoteInfo && this.quoteInfo.level === 'MSP';
        console.log('isMspProduct check:', {
            quoteInfo: this.quoteInfo,
            level: this.quoteInfo?.level,
            isMsp: isMsp
        });
        return isMsp;
    }

    // 判断是否为算力产品报价
    get isComputingProduct() {
        const isComputing = this.quoteInfo && this.quoteInfo.level === '算力';
        return isComputing;
    }

    // 判断是否为AI基础设施产品报价
    get isAIInfrastructureProduct() {
        const isAI = this.quoteInfo && this.quoteInfo.level === 'AI基础设施';
        return isAI;
    }
    get isAISearchProduct() {
        const isAIS = this.quoteInfo && this.quoteInfo.level === 'AI Search';
        return isAIS;
    }

    // 判断是否需要查询牌价的产品线（MaaS 和 AI Search）
    get shouldQueryListPrice() {
        return this.isMassProduct || this.isAISearchProduct;
    }


    // 获取当前已有的产品列表，用于重复校验
    get currentExistingProducts() {
        let existingProducts = [];

        if (this.groupIndex === 'single') {
            if (this.singleProducts && this.singleProducts.length > 0) {
                existingProducts = [...this.singleProducts];
            }
            console.log('单产品重复校验 - 当前已有单产品数量:', existingProducts.length);
        } else {
            const currentGroupIndex = parseInt(this.groupIndex, 10);
            if (this.productGroups && this.productGroups[currentGroupIndex] && this.productGroups[currentGroupIndex].products) {
                existingProducts = [...this.productGroups[currentGroupIndex].products];
            }
            console.log('产品组重复校验 - 当前产品组索引:', currentGroupIndex, '已有产品数量:', existingProducts.length);
        }

        return existingProducts;
    }
    // 计算待保存的临时产品数量
    get tempProductsCount() {
        let count = 0;
        this.tempProducts.forEach(tempData => {
            count += tempData.products ? tempData.products.length : 0;
        });
        this.tempSingleProducts.forEach(tempData => {
            count += tempData.products ? tempData.products.length : 0;
        });
        return count;
    }

    // 是否有临时产品等待保存
    get hasTempProducts() {
        return this.tempProductsCount > 0;
    }
    @track showSingleProductQuoteModal = false; // 控制单产品报价方式模态框显示状态
    @track currentSingleProduct = null; // 当前操作的单产品
    @track currentSingleProductIndex = null; // 当前操作的单产品索引
    @track showCustomerIdsModal = false; // 控制客户账号id配置模态框显示状态
    @track currentCustomerAccountId = ''; // 当前配置的客户账号id字符串
    @track currentProductId = ''; // 当前配置的产品id
    @track currentProductGroupIndex = null; // 当前配置客户账号id的产品所属产品组索引
    get quoteTypeOptions() {
        // 获取当前报价的产品线
        const productLine = this.quoteInfo?.level || '';
        // 判断当前是配置单产品还是产品组
        const isConfiguringSingleProduct = this.currentConfigSingleIndex !== null;

        console.log('当前产品线2222:', productLine, '是否配置单产品:', isConfiguringSingleProduct);
        
        // 基础选项：单价*数量（所有产品线都有）
        const options = [
            { label: '单价*数量', value: '1' }
        ];

        // 根据产品线添加相应的报价方式
        switch (productLine.toUpperCase()) {
            case 'MSP':
                options.push({ label: '产品折扣', value: '2' });
               
                if (isConfiguringSingleProduct) {
                    options.push({ label: '阶梯金额折扣', value: '4' });
                }
                break;

            case 'AI SEARCH':
            case 'MAAS':
            case 'MaaS':
                options.push({ label: '产品折扣', value: '2' });
               
                if (isConfiguringSingleProduct) {
                    options.push({ label: '阶梯用量单价', value: '3' });
                }
               
                options.push({ label: '阶梯金额折扣', value: '4' });
               
                
                if (!isConfiguringSingleProduct) {
                    options.push({ label: '保底金额+共享阶梯金额折扣', value: '5' });
                }
                break;

            case '智能算力':
                
                if (isConfiguringSingleProduct) {
                    options.push({ label: '阶梯用量单价', value: '3' });
                }
                options.push({ label: '单价*数量+阶梯用量单价', value: '6' });
                break;

            case 'AI基础设施':
               
                if (isConfiguringSingleProduct) {
                    options.push({ label: '阶梯用量单价', value: '3' });
                }
                options.push({ label: '单价*数量+阶梯用量单价', value: '6' });
                break;
           
            default:
                
                console.log('未识别的产品线，显示所有报价方式选项');
                options.push({ label: '产品折扣', value: '2' });
                
                if (isConfiguringSingleProduct) {
                    options.push(
                        { label: '阶梯用量单价', value: '3' },
                        { label: '阶梯金额折扣', value: '4' }
                    );
                }
               
                if (!isConfiguringSingleProduct) {
                    options.push({ label: '保底金额+共享阶梯金额折扣', value: '5' });
                }
                options.push({ label: '单价*数量+阶梯用量单价', value: '6' });
                break;
        }

        console.log('可用报价方式:', options);
        return options;
    };
    @track showQuoteConfigModal = false;
    @track currentConfigGroupIndex = null;
    @track currentConfigGroup = null;
    @track currentConfigGroupNumber = null;
    @track regionOptions = [
      
    ];
    // 产品表格列定义
    get productColumns() {
        const columns = [
        { label: '产品编号', fieldName: 'ProductCode', type: 'text', initialWidth: 100, wrapText: true },
        { label: '产品名称', fieldName: 'Name', type: 'text', initialWidth: 180, wrapText: true },
        { label: '一级产品', fieldName: 'level1', type: 'text', initialWidth: 120, wrapText: true },
        { label: '二级产品', fieldName: 'level2', type: 'text', initialWidth: 120, wrapText: true },
        { label: '三级产品', fieldName: 'level3', type: 'text', initialWidth: 120, wrapText: true },
        { label: '产品描述', fieldName: 'ProductDescription', type: 'text', initialWidth: 200, wrapText: true },

        { label: '计量单位', fieldName: 'QuantityUnitOfMeasure', type: 'text', initialWidth: 100, wrapText: true },
        { label: '存在汇率差', fieldName: 'exchangeRateDifferenceLabel', type: 'text', initialWidth: 100, wrapText: true,editable:false },
        ];

        // 只有MSP产品线才显示客户账号Id列
        if (this.isMspProduct) {
            columns.push({ label: '客户账号Id', fieldName: 'customerAccountId', type: 'text', initialWidth: 120 });
        }

        // 继续添加其他列
        columns.push(

        // 只有MaaS和AI Search产品线才显示牌价相关字段
        ...(this.shouldQueryListPrice ? [
            { label: '牌价', fieldName: 'listPrice', type: 'number', initialWidth: 100,
              typeAttributes: {
                  minimumFractionDigits: 7,
                  maximumFractionDigits: 10
              }
            },
            { label: '牌价币种', fieldName: 'listPriceCurrency', type: 'text', initialWidth: 90 },
           
        ] : []),

         { label: '是否一次性费用', fieldName: 'OneTimeFee', editable: this.isEditMode, initialWidth: 160,
                  type: 'picklistColumn',
                    typeAttributes: {
                        placeholder: 'Choose Type',
                        options: [{label :'是', value:'是'},{label :'否', value:'否'}],
                        value: { fieldName: 'OneTimeFee' },
                        label: { fieldName: 'OneTimeFee' },
                        context: { fieldName: 'Id' }
                    }
            },
        { label: '计算类型', fieldName: 'chargeType', editable: this.isEditMode, initialWidth: 100,
                type: 'picklistColumn',
                typeAttributes: {
                    placeholder: 'Choose Type',
                    options: [{label :'计费', value:'计费'},{label :'计量', value:'计量'}],
                    value: { fieldName: 'chargeType' },
                    label: { fieldName: 'chargeType' },
                    context: { fieldName: 'Id' }
                }
        },
        { label: '计费项说明', fieldName: 'ChargeExplanation', type: 'text', editable: this.isEditMode, initialWidth: 150, wrapText: true },
        // 只有MSP产品线才显示汇率差系数
        ...(this.isMspProduct ? [{
            label: '汇率差系数',
            fieldName: 'unitPrice',
            type: 'number',
            initialWidth: 120,
            editable: true,
            typeAttributes: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }
        }] : []),
            { label: '利润率（%）', fieldName: 'taxRate', type: 'number', editable: this.isEditMode, required: true, initialWidth: 100,
            typeAttributes: {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
                required: true
            },
            cellAttributes: {
                class: { fieldName: 'taxRateClass' }
            }
        },
             { label: '利润说明', fieldName: 'profitDescription', type: 'text', editable: this.isEditMode, initialWidth: 150, wrapText: true },
             //只有MaaS和AI Search产品线才显示牌价区域
             ... (this.shouldQueryListPrice ? [
                { label: '牌价区域', fieldName: 'productAreaLabel', type: 'text', initialWidth: 120}
            ] : []),
            { label: '区域', fieldName: 'Region', editable: this.isEditMode, initialWidth: 100,
                  type: 'picklistColumn',
                    typeAttributes: {
                        placeholder: 'Choose Type',
                        options: this.regionOptions,
                        value: { fieldName: 'Region' },
                        label: { fieldName: 'Region' },
                        context: { fieldName: 'Id' }
                    }
            },
            { label: '备注', fieldName: 'Description', type: 'text', editable: this.isEditMode, initialWidth: 150, wrapText: true },
            { label: '服务开始日', fieldName: 'QuoteLineStartDate', type: 'date', editable: this.isEditMode, initialWidth: 130 },
            { label: '服务截止日', fieldName: 'QuoteLineEndDate', type: 'date', editable: this.isEditMode, initialWidth: 130 }
        );

        // 只在编辑模式下且为MSP产品线时添加action列
        if (this.isEditMode && this.isMspProduct) {
            columns.push({
                type: 'action',
                typeAttributes: {
                    rowActions: [
                        { label: '配置客户账号id', name: 'ConfigGroupProductCustAccountID' },
                    ]
                }
            });
        }

        return columns;
        
    }
    // @track actions=[];
    // 单产品表格列定义
    get singleProductColumns() {
        const columns = [
             {
                type: 'action',
                typeAttributes: {
                    // rowActions: [
                    //     { label: '报价方式', name: 'quote_type' },
                    //     { label: '复制', name: 'clone' }
                    // ]
                    rowActions: { fieldName: 'dynamicActions' }
                }
            },
            { label: '产品编号', fieldName: 'ProductCode', type: 'text', initialWidth: 100 },
            { label: '产品名称', fieldName: 'Name', type: 'text', initialWidth: 180 },
            { label: '一级产品', fieldName: 'level1', type: 'text', initialWidth: 120 },
            { label: '二级产品', fieldName: 'level2', type: 'text', initialWidth: 120 },
            { label: '三级产品', fieldName: 'level3', type: 'text', initialWidth: 120},
            { label: '产品描述', fieldName: 'ProductDescription', type: 'text', initialWidth: 200 },

            { label: '报价方式', fieldName: 'quoteTypeLabel', type: 'text', initialWidth: 140},
            { label: '计量单位', fieldName: 'QuantityUnitOfMeasure', type: 'text', initialWidth: 100 },
            { label: '存在汇率差', fieldName: 'exchangeRateDifferenceLabel', type: 'text', initialWidth: 100, editable:false },
        ];

        // 只有MSP产品线才显示客户账号Id列
        if (this.isMspProduct) {
            columns.push({ label: '客户账号Id', fieldName: 'customerAccountId', type: 'text', initialWidth: 120 });
        }

        // 继续添加其他列
        columns.push(

        // 只有MaaS和AI Search产品线才显示牌价相关字段
        ...(this.shouldQueryListPrice ? [
            { label: '牌价', fieldName: 'listPrice', type: 'number', initialWidth: 100,
              typeAttributes: {
                  minimumFractionDigits: 7,
                  maximumFractionDigits: 10
              }
            },
            { label: '牌价币种', fieldName: 'listPriceCurrency', type: 'text', initialWidth: 90 },
        ] : []),
             { label: '是否一次性费用', fieldName: 'OneTimeFee', editable: this.isEditMode, initialWidth: 160,
                  type: 'picklistColumn',
                    typeAttributes: {
                        placeholder: 'Choose Type',
                        options: [{label :'是', value:'是'},{label :'否', value:'否'}],
                        value: { fieldName: 'OneTimeFee' },
                        label: { fieldName: 'OneTimeFee' },
                        context: { fieldName: 'Id' }
                    }
            },
             { label: '计算类型', fieldName: 'chargeType', editable: this.isEditMode, initialWidth: 100,
                type: 'picklistColumn',
                typeAttributes: {
                    placeholder: 'Choose Type',
                    options: [{label :'计费', value:'计费'},{label :'计量', value:'计量'}],
                    value: { fieldName: 'chargeType' },
                    label: { fieldName: 'chargeType' },
                    context: { fieldName: 'Id' }
                }
            },
            { label: '计费项说明', fieldName: 'ChargeExplanation', type: 'text', editable: this.isEditMode, initialWidth: 150, wrapText: true },
            // 只有MSP产品线才显示汇率差系数
            ...(this.isMspProduct ? [{
                label: '汇率差系数',
                fieldName: 'unitPrice',
                type: 'number',
                initialWidth: 120,
                editable: true,
                typeAttributes: {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }
            }] : []),
            { 
                label: '利润率（%）', 
                fieldName: 'taxRate', 
                type: 'number', 
                editable: this.isEditMode, 
                required: true,
                initialWidth: 100,
                typeAttributes: { 
                    minimumFractionDigits: 2, 
                    maximumFractionDigits: 2,
                    required: true
                },
                cellAttributes: {
                    class: { fieldName: 'taxRateClass' }    
                }
            },
            { label: '利润说明', fieldName: 'profitDescription', type: 'text', editable: this.isEditMode, initialWidth: 150, wrapText: true },
            //如果是maas和AIsearch产品线则展示牌价区域
            ... (this.shouldQueryListPrice ? [
                { label: '牌价区域', fieldName: 'productAreaLabel', type: 'text', initialWidth: 120}
            ] : []),
            
            { label: '区域',  fieldName: 'Region',  editable: this.isEditMode, initialWidth: 100,
                  type: 'picklistColumn',
                    typeAttributes: {
                        placeholder: 'Choose Type',
                        options: this.regionOptions,
                        value: { fieldName: 'Region' },
                        label: { fieldName: 'Region' },
                        context: { fieldName: 'Id' } 
                    }
            },
            { label: '备注', fieldName: 'Description', type: 'text', editable: this.isEditMode, initialWidth: 150, wrapText: true },
            { label: '服务开始日', fieldName: 'QuoteLineStartDate', type: 'date', editable: this.isEditMode, initialWidth: 130 },
            { label: '服务截止日', fieldName: 'QuoteLineEndDate', type: 'date', editable: this.isEditMode, initialWidth: 130 }
        );

        return columns;
    }
  
    
    // 计算方式选项
    calculationMethodOptions = [
        { label: '落区', value: '01' },
        { label: '分区', value: '02' }
    ];

    // 保底类型选项
    minimumGuaranteeTypeOptions = [
        { label: '折前', value: '1' },
        { label: '折后', value: '2' }
    ];

    // 折扣类型选项
    discountTypeOptions = [
        { label: '产品折扣', value: '1' },
        { label: '服务费', value: '2' }
    ];

    // 单位选项
    unitOptions = [
        { label: '金额', value: '金额' },
        { label: '用量', value: '用量' }
    ];

    // 是否赠送选项
    isGiftOptions = [
        { label: '否', value: '2' },
        { label: '是', value: '1' }
    ];
    
    // 页面引用，用于获取recordId
    @wire(CurrentPageReference)
    getPageReference(pageRef) {
        if (pageRef) {
            // 从URL参数中获取报价ID
            if (pageRef.state && pageRef.state.c__recordId) {
                this.recordId = pageRef.state.c__recordId;
                // 从URL参数中获取startInEditMode
                if (pageRef.state.c__startInEditMode === 'true') {
                    this.startInEditMode = true;
                    this.isEditMode = true;
                }
            } else if (pageRef.attributes && pageRef.attributes.recordId) {
                this.recordId = pageRef.attributes.recordId;
            }
            console.log('QuoteLineGroupCreate - 当前报价ID:', this.recordId);
            console.log('QuoteLineGroupCreate - 编辑模式:', this.startInEditMode);
            
            // 加载数据
            // this.loadProductGroups();
        }
    }
     
    @wire(getPicklistValues, {
        objectApiName: 'QuoteLineItem', 
        fieldApiName: 'Region__c'       
    })
    wiredRegionPicklist({ error, data }) {
        if (data) {
            console.log('区域字段 Picklist 值:', data);
            this.regionOptions = Object.entries(data).map(([label, value]) => ({
                label: label, 
                value: label   
            }));
            console.log('区域字段 Picklist 值:', this.regionOptions);
        } else if (error) {
            console.error('获取 Picklist 失败:', error);
        }
    }

    // 组件连接到DOM时的回调
    connectedCallback() {
        console.log('组件初始化，报价ID:', this.recordId);
        //
        

        // 加载报价单信息
        if (this.recordId) {
            getQuoteInfo({ quoteId: this.recordId })
                .then(result => {
                    console.log('获取报价单信息成功:', JSON.stringify(result));
                    this.quoteInfo = result;
                    this.QuoteName = result.name;
                    this.quoteLevel=result.level;
                    this.exchangeRateDifference= result.exchangeRateDifferenceLabel;
                    // 只有在获取到报价单信息后才加载产品数据
                    this.loadProductGroups();
                    this.loadSingleProducts();
                })
                .catch(error => {
                    console.error('获取报价单信息失败:', this.reduceErrors(error));
                });
        } else {
            // 如果没有recordId，初始化默认的quoteInfo
            this.quoteInfo = {
                exchangeRateDifferenceValue: '2', // 默认值：否
                exchangeRateDifferenceLabel: '否'
            };
            this.exchangeRateDifference = '否';

            // 加载产品组数据
            this.loadProductGroups();

            // 加载单产品数据
            this.loadSingleProducts();
        }
        
        // 如果设置了startInEditMode，自动进入编辑模式
        if (this.startInEditMode) {
            console.log('设置编辑模式 - 通过startInEditMode');
            this.isEditMode = true;
        }
    }

    
    // 加载产品组数据
    loadProductGroups() {
        console.log('加载产品组数据，报价ID:', this.recordId);
        console.log('加载产品组数据，编辑模式:', this.startInEditMode);

        if (!this.recordId) {
            console.log('报价ID为空，初始化空产品组');
            // this.initializeEmptyGroups();
            // 如果没有记录ID且设置了startInEditMode，自动进入编辑模式
            if (this.startInEditMode) {
                console.log('设置编辑模式 - 无记录ID');
                this.isEditMode = true;
            }
            return;
        }
        
        this.isLoading = true;
        
        // 加载产品组数据
        getProductGroups({ 
            quoteId: this.recordId
        })
            .then(data => {
                console.log('获取产品组数据成功:', JSON.stringify(data));
                if(data.length == 0){
                    return;
                }

                if (data && data.length > 0) {
                    this.productGroups = this.processGroupData(data);
                } 
                // else {
                //     this.initializeEmptyGroups();
                // }
                
                // 如果设置了startInEditMode，自动进入编辑模式
                if (this.startInEditMode) {
                    console.log('设置编辑模式 - 有记录ID');
                    this.isEditMode = true;
                }
            })
            .catch(error => {
                console.error('获取产品组数据失败:', this.reduceErrors(error));
                this.showError('获取产品组数据失败: ' + this.reduceErrors(error));
                // this.initializeEmptyGroups();
            })
            .finally(() => {
                this.isLoading = false;
            });
    }
    
    // 处理从后端获取的产品组数据
    processGroupData(data) {
        return data.map((group, index) => {
            console.log('处理产品组数据，组ID:', group.id, '产品数量:', group.products ? group.products.length : 0);
            console.log('产品组的ladderDiscountId:', group.ladderDiscountId);
            
            // 如果存在产品，尝试从产品中提取ladderDiscountId
            let ladderDiscountIdFromProducts = null;
            if (Array.isArray(group.products) && group.products.length > 0) {
                for (let product of group.products) {
                    if (product.ladderDiscountId) {
                        ladderDiscountIdFromProducts = product.ladderDiscountId;
                        console.log('从产品中提取到ladderDiscountId:', ladderDiscountIdFromProducts);
                        break;
                    }
                }
            }
            
            // 检查是否是组合报价方式，如果是，需要确保相应的基础报价方式标志也被设置
            const isMinAmountSharedLadder = group.hasMinAmountSharedLadderAmountDiscountDown === true;
            const isMinUnitPriceSharedLadder = group.hasMinUnitPriceSharedLadderUsagePriceDown === true;
            
            // 如果是保底金额+共享阶梯金额折扣落区，确保hasMinimumAmountQuote也为true
            const hasMinimumAmountQuote = isMinAmountSharedLadder || group.hasMinimumAmountQuote === true;
            
            // 如果是单价*数量+共享阶梯用量单价落区，确保hasMinimumUnitPriceQuote也为true
            const hasMinimumUnitPriceQuote = isMinUnitPriceSharedLadder || group.hasMinimumUnitPriceQuote === true;
            
            // 计算当前产品组的报价方式值
            // let quoteTypeValue = '';
            // if (isMinAmountSharedLadder) {
            //     quoteTypeValue = '保底金额+共享阶梯金额折扣';
            // } else if (isMinUnitPriceSharedLadder) {
            //     quoteTypeValue = '单价*数量+共享阶梯用量单价';
            // // } else if (group.hasFixedAmountQuote) {
            // //     quoteTypeValue = '固定金额'; // 注释掉固定金额报价方式
            // } else if (group.hasProductDiscountQuote) {
            //     quoteTypeValue = '产品折扣';
            // } else if (group.hasMinimumUnitPriceQuote) {
            //     quoteTypeValue = '单价*数量';
            // // } else if (group.hasSharedLadderAmountDiscountZone) {
            // //     quoteTypeValue = '共享阶梯金额折扣，分区计算';
            // } else if (group.hasSharedLadderAmountDiscountDown) {
            //     quoteTypeValue = '共享阶梯金额折扣，落区计算';
            // } else if (group.hasSharedLadderUsagePriceDown) {
            //     quoteTypeValue = '阶梯用量单价';
            // }
            
            // 确保组合报价方式的保底数据正确显示
            let minimumAmount = group.minimumAmount;
            let minimumUnitPrice = group.minimumUnitPrice;
            let minimumQuantity = group.minimumQuantity;
            
            // 如果是保底金额+共享阶梯金额折扣落区，但保底金额为空，尝试从其他字段获取
            if (isMinAmountSharedLadder && (!minimumAmount || minimumAmount === 'null')) {
                console.log('保底金额+共享阶梯金额折扣落区的保底金额为空，尝试从其他字段获取');
                // 可能在其他字段中存储了保底金额数据
                if (group.GuaranteedMin_Amount__c) {
                    minimumAmount = group.GuaranteedMin_Amount__c;
                }
            }
            
            // 如果是单价*数量+共享阶梯用量单价落区，但保底单价或保底数量为空，尝试从其他字段获取
            if (isMinUnitPriceSharedLadder) {
                console.log('单价*数量+阶梯用量单价落区的保底单价或保底数量为空，尝试从其他字段获取');
                
                // 可能在其他字段中存储了保底单价和保底数量数据
                if (!minimumUnitPrice || minimumUnitPrice === 'null') {
                    if (group.Minimum_ProdUnitPrice__c) {
                        minimumUnitPrice = group.Minimum_ProdUnitPrice__c;
                    }
                }
                
                if (!minimumQuantity || minimumQuantity === 'null') {
                    if (group.Minimum_Amout__c) {
                        minimumQuantity = group.Minimum_Amout__c;
                    }
                }
            }
            
            // 处理阶梯数据，确保单位和计算方式正确
            let tiers = Array.isArray(group.tiers) ? group.tiers.map((tier, tierIndex, tiersArray) => {
                let unitNotEditable = true;
                let calculationMethodNotEditable = true;
                
                // 根据报价方式类型设置单位和计算方式
                if (isMinAmountSharedLadder || group.hasSharedLadderAmountDiscountDown) {
                    tier.unit = '金额';
                    tier.calculationMethod = '落区';
                } else if (isMinUnitPriceSharedLadder || group.hasSharedLadderUsagePriceDown) {
                    tier.unit = '用量';
                    tier.calculationMethod = '落区';
                } else if (group.hasSharedLadderAmountDiscountZone) {
                    tier.unit = '金额';
                    tier.calculationMethod = '分区';
                }
                
                // 只有最后一个阶梯可删除
                const isDeletable = tierIndex === (tiersArray.length - 1);
                
                return {
                    ...tier,
                    id: tier.id || ('temp_tier_' + Date.now() + '_' + tierIndex),
                    unitNotEditable,
                    calculationMethodNotEditable,
                    isDeletable
                };
            }) : [];
            
            const processedProducts = Array.isArray(group.products) ? this.processProductData(group.products) : [];
            const maasLevelThreeProducts = processedProducts.filter(p => p.level1 === 'MaaS');
            const levelThreeProductNames = [...new Set(maasLevelThreeProducts.map(p => p.level3).filter(Boolean))];
            const tempprocessedProducts = processedProducts.map((product) => {
                return {
                    ...product,
                    exchangeRateDifferenceLabel: this.exchangeRateDifference,
                    exchangeRateDifferenceValue: this.quoteInfo?.exchangeRateDifferenceValue || '2'
                };
            });

            // 处理三级产品标签显示逻辑
            const allLevelThreeProducts = levelThreeProductNames.map(name => ({ name, id: (group.id || 'temp_group') + '_' + name }));
            const displayedLevelThreeProducts = allLevelThreeProducts.slice(0, 4).map(product => ({ ...product, shouldDisplay: true }));
            const hasMoreTags = allLevelThreeProducts.length > 4;
            const remainingCount = Math.max(0, allLevelThreeProducts.length - 4);
            const moreTagsLabel = `更多 (${remainingCount})`;

            return {
                ...group,
                id: group.id && !group.id.startsWith('temp_') ? group.id : null,
                groupNumber: index + 1,
                products: tempprocessedProducts,
                tiers: tiers,
                levelThreeProducts: displayedLevelThreeProducts,
                allLevelThreeProducts: allLevelThreeProducts, // 保存所有标签用于弹窗显示
                hasMoreTags: hasMoreTags,
                moreTagsLabel: moreTagsLabel,
                // 强制删除的报价方式设为false，即使后端数据可能是true
                hasLadderQuote: false, // 阶梯报价 - 已删除
                // 修改这里，使用上面计算的组合值
                hasMinimumAmountQuote: hasMinimumAmountQuote, // 保底金额 - 如果是组合报价方式也需要设置为true
                hasMinimumUnitPriceQuote: hasMinimumUnitPriceQuote, // 保底单价*保底数量 - 如果是组合报价方式也需要设置为true
                hasFixedUsageQuote: false, // 固定用量*固定单价 - 已删除
                hasMaximumAmountQuote: false, // 金额封顶 - 已删除
                // 保留的报价方式
                hasProductDiscountQuote: group.hasProductDiscountQuote === true,
                // 使用从后端获取的其他报价方式状态
                // hasFixedAmountQuote: group.hasFixedAmountQuote === true, // 注释掉固定金额报价方式
                hasSharedLadderAmountDiscountZone: group.hasSharedLadderAmountDiscountZone === true, // 注释掉共享阶梯金额折扣分区
                hasSharedLadderAmountDiscountDown: group.hasSharedLadderAmountDiscountDown === true, // 保留，因为组合报价方式需要
                hasSharedLadderUsagePriceDown: group.hasSharedLadderUsagePriceDown === true,
                hasMinAmountSharedLadderAmountDiscountDown: isMinAmountSharedLadder,
                hasMinUnitPriceSharedLadderUsagePriceDown: isMinUnitPriceSharedLadder,
                // 使用处理后的保底数据
                minimumAmount: minimumAmount || null,
                minimumUnitPrice: minimumUnitPrice || null,
                minimumQuantity: minimumQuantity || null,
                // 是否赠送字段处理
                isGift: group.isGift || '2', // 默认为否
                isGiftLabel: group.isGift === '1' ? '是' : '否',
                // 保底类型处理
                minimumGuaranteeType: group.minimumGuaranteeType || '1', // 默认为折前
                minimumGuaranteeTypeLabel: this.getMinimumGuaranteeTypeLabel(group.minimumGuaranteeType || '1'),
                fixedUsage: group.fixedUsage || null,
                // fixedAmount: group.fixedAmount || null, // 注释掉固定金额报价方式
                maximumAmount: group.maximumAmount || null,
                discountCoefficient: group.discountCoefficient || null,
                fixedRebate: group.fixedRebate || null,
                cashReduce: group.cashReduce || null,
                credit: group.credit || null,
                // 其他字段
                fixedPrice: group.fixedPrice || null,
                fixedQuantity: group.fixedQuantity || null,
                hasAnyQuoteType:
                    // group.hasProductDiscountQuote === true ||
                    // group.hasFixedAmountQuote === true || // 注释掉固定金额报价方式
                    // group.hasSharedLadderAmountDiscountZone === true || // 注释掉共享阶梯金额折扣分区
                    // group.hasSharedLadderAmountDiscountDown === true || // 注释掉共享阶梯金额折扣落区
                    // group.hasSharedLadderUsagePriceDown === true ||
                    // isMinAmountSharedLadder ||
                    // isMinUnitPriceSharedLadder,
                    group.quoteTypeValue != null && group.quoteTypeValue != '',
                // 存储Ladder_discount__c值作为产品组的唯一标识符
                ladderDiscountId: group.ladderDiscountId || ladderDiscountIdFromProducts || this.extractLadderDiscountId(group.products),
                // 添加当前报价方式值
                quoteTypeValue: group.quoteTypeValue,
                quoteTypeLabel: group.quoteTypeLabel,
                // 阶梯类型和折扣类型
                ladderType: group.ladderType || '01', // 默认为落区
                discountType: group.discountType || '1' // 默认为产品折扣
                

        
            };
        });
    }

    // 获取保底类型标签
    getMinimumGuaranteeTypeLabel(value) {
        const option = this.minimumGuaranteeTypeOptions.find(opt => opt.value === value);
        return option ? option.label : '折前';
    }

    // 获取折扣类型标签
    getDiscountTypeLabel(value) {
        const option = this.discountTypeOptions.find(opt => opt.value === value);
        return option ? option.label : '产品折扣';
    }

    // 从产品列表中提取Ladder_discount__c值作为产品组的唯一标识符
    extractLadderDiscountId(products) {
        if (!Array.isArray(products) || products.length === 0) {
            return null;
        }
        
        // 尝试查找产品中的Ladder_discount__c值
        for (const product of products) {
            if (product.ladderDiscountId) {
                return product.ladderDiscountId;
            }
        }
        
        return null;
    }
    
    // 处理产品数据
    processProductData(products) {
        return products.map((product, index) => {
            // 计算汇率差系数
            let unitPrice = product.unitPrice;

            // 根据产品线和汇率差情况设置汇率差系数
            console.log('processProductData - 检查unitPrice:', {
                originalUnitPrice: unitPrice,
                isMspProduct: this.isMspProduct,
                exchangeRateDifferenceValue: this.quoteInfo?.exchangeRateDifferenceValue,
                productName: product.productName || product.Name
            });

            // 对于MSP产品线且存在汇率差的情况，强制设置为1.02
            if (this.isMspProduct && this.quoteInfo?.exchangeRateDifferenceValue === '1') {
                unitPrice = 1.02;
                console.log('MSP产品线存在汇率差，强制设置汇率差系数为1.02');
            } else if (!unitPrice || unitPrice === '' || unitPrice === null || unitPrice === undefined) {
                // 其他情况下，如果unitPrice为空，则设置为1.0
                unitPrice = 1.0;
                console.log('设置默认汇率差系数为1.0');
            } else {
                console.log('保持原有unitPrice值:', unitPrice);
            }

            return {
                ...product,
                id: product.id || ('temp_product_' + Date.now() + '_' + index),
                productName: product.productName || '未知产品',
                unitPrice: unitPrice
            };
        });
    }
    
    // 初始化空产品组
    initializeEmptyGroups() {
        console.log('初始化空产品组');
        this.productGroups = [this.createEmptyGroup(1)];
        console.log('产品组初始化完成:', JSON.stringify(this.productGroups));
    }
    
    // 创建空产品组
    createEmptyGroup(groupNumber) {
        return {
            id: 'temp_group_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            groupNumber: groupNumber,
            products: [],
            tiers: [],
            // 已删除的五种报价方式设为false
            hasLadderQuote: false,
            hasMinimumAmountQuote: false,
            hasMinimumUnitPriceQuote: false,
            hasFixedUsageQuote: false,
            hasMaximumAmountQuote: false,
            // 保留的报价方式
            hasProductDiscountQuote: false,
            // 其他报价方式
            // hasFixedAmountQuote: false, // 注释掉固定金额报价方式
            hasSharedLadderAmountDiscountZone: false,
            hasSharedLadderAmountDiscountDown: false,
            hasSharedLadderUsagePriceDown: false,
            hasMinAmountSharedLadderAmountDiscountDown: false,
            hasMinUnitPriceSharedLadderUsagePriceDown: false,
            // 字段值初始化
            minimumAmount: null,
            minimumGuaranteeType: '1', // 默认为折前
            minimumGuaranteeTypeLabel: '折前',
            minimumUnitPrice: null,
            minimumQuantity: null,
            expandMultiple: 1, // 扩大倍数字段，默认值为1
            fixedUsage: null,
            // fixedAmount: null, // 注释掉固定金额报价方式
            maximumAmount: null,
            discountCoefficient: null,
            fixedRebate: null,
            cashReduce: null,
            credit: null,
            fixedPrice: null,
            fixedQuantity: null,
            ladderType: '01', // 默认阶梯类型为落区
            discountType: '1', // 默认折扣类型为产品折扣
            isGift: '2', // 默认为否
            isGiftLabel: '否',
            hasAnyQuoteType: false
        };
    }
    
    // 创建空产品
    // createEmptyProduct() {
    //     return {
    //         id: 'temp_product_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
    //         productId: '',
    //         productName: '未知产品',
    //         ProductCode: '',
    //         Name: '',
    //         Family: '',
    //         Description: '',
    //         ProductDescription: '',
    //         QuantityUnitOfMeasure: '',
    //         taxRate: '',
    //         unitPrice: '',
    //         customerAccountId: '',
    //         profitDescription: '',
    //         Region: '',
    //         OneTimeFee: '否',
    //         QuoteLineStartDate: this.quoteInfo?.startDate || null,
    //         QuoteLineEndDate: this.quoteInfo?.endDate || null
    //     };
    // }
    
    // 创建空阶梯
    createEmptyTier() {
        return {
            id: 'temp_tier_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            lowerBound: '',
            upperBound: '',
            unit: '用量', // 默认设置为"用量"
            discount: '',
            calculationMethod: '分区',
            notEditable: false, // 默认可编辑
            unitNotEditable: false, // 单位默认可编辑
            calculationMethodNotEditable: false, // 计算方式默认可编辑
            isDeletable: true // 默认可删除
        };
    }
    
    // 添加产品组
    handleAddGroup() {
        console.log('添加产品组');
        const newGroup = this.createEmptyGroup(this.productGroups.length + 1);
        this.productGroups = [...this.productGroups, newGroup];
        console.log('产品组添加完成，当前产品组数量:', this.productGroups.length);
    }
    closeModal(event){
        this.isModalOpen = false;
        this.showQuoteConfigModal = false;
    }

    // 处理添加产品按钮点击
    handleAddProductClick(event) {
        this.groupIndex = event.currentTarget.dataset.groupIndex
        console.log("eventCurrentTarg" + event.currentTarget.dataset.groupIndex);
        console.log("eventTarg"+event.target.dataset.groupIndex);
        // console.log('跳转到产品选择页面，recordId:', this.recordId, '产品组索引:', groupIndex);
        console.log('当前产品组索引**************************88:', this.groupIndex);
        this.isModalOpen = true;
        // 直接使用URL打开ProductInfo组件
        // let url = '/lightning/cmp/c__productInfoWrapper';
        
        // // 添加URL参数
        // const params = new URLSearchParams();
        // if (this.recordId) {
        //     params.append('c__recordId', this.recordId);
        // }
        // params.append('c__groupIndex', groupIndex);
        
        // // 在新窗口打开
        // window.open(`${url}?${params.toString()}`, '_blank');
        
        // 添加事件监听器，检测子窗口关闭
        // this.checkProductSelection();
    }
    
    // 删除产品组
    handleDeleteGroup(event) {
        const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
        console.log('删除产品组，索引:', groupIndex);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            const groupToDelete = this.productGroups[groupIndex];

            // 获取要删除的产品组中的产品ID和产品ID列表
            const productIdsToDelete = groupToDelete.products.map(product => product.id);
            const productProductIdsToDelete = groupToDelete.products.map(product => product.productId);
            console.log('要删除的产品组中的产品IDs:', productIdsToDelete);
            console.log('要删除的产品组中的产品ProductIDs:', productProductIdsToDelete);

            // 从临时产品列表中删除相关的产品
            this.tempProducts = this.tempProducts.filter(tempData => tempData.groupIndex !== groupIndex);

            // 从临时单产品列表中删除相关的产品
            this.tempSingleProducts = this.tempSingleProducts.map(tempData => {
                const filteredProducts = tempData.products.filter(product =>
                    !productIdsToDelete.includes(product.id) &&
                    !productProductIdsToDelete.includes(product.productId)
                );
                return {
                    ...tempData,
                    products: filteredProducts
                };
            }).filter(tempData => tempData.products.length > 0);

            // 从单产品列表中删除相关的产品（基于ID和productId进行匹配）
            this.singleProducts = this.singleProducts.filter(product =>
                !productIdsToDelete.includes(product.id) &&
                !productProductIdsToDelete.includes(product.productId)
            );

            // 创建新的数组，排除要删除的产品组
            this.productGroups = this.productGroups.filter((_, index) => index !== groupIndex);

            // 更新剩余产品组的编号
            this.productGroups = this.productGroups.map((group, index) => {
                return {...group, groupNumber: index + 1};
            });

            console.log('产品组删除完成，剩余产品组数量:', this.productGroups.length);
            console.log('清理后的临时产品数量:', this.tempProducts.length);
            console.log('清理后的临时单产品数量:', this.tempSingleProducts.length);
            console.log('清理后的单产品数量:', this.singleProducts.length);

            // // 重新加载单产品数据，确保前端显示与后端一致
            // this.loadSingleProducts();

            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '产品组已删除',
                    variant: 'success'
                })
            );
        }
    }
    

    
    // 删除产品
    handleDeleteProduct(event) {
        const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
        const productIndex = parseInt(event.currentTarget.dataset.productIndex, 10);
        console.log(`删除产品，产品组索引: ${groupIndex}, 产品索引: ${productIndex}`);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length &&
            productIndex >= 0 && productIndex < this.productGroups[groupIndex].products.length) {

            const productToDelete = this.productGroups[groupIndex].products[productIndex];

            // 如果是临时产品（ID以temp_开头），从临时列表中删除
            if (productToDelete.id && productToDelete.id.startsWith('temp_')) {
                this.removeTempProduct(groupIndex, productToDelete.id);
            }

            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex].products = updatedGroups[groupIndex].products.filter((_, index) => index !== productIndex);

            this.productGroups = updatedGroups;
            console.log('产品删除完成，剩余产品数量:', this.productGroups[groupIndex].products.length);
        }
    }

    // 从临时产品列表中删除指定产品
    removeTempProduct(groupIndex, productId) {
        console.log('从临时列表删除产品:', productId);

        // 从临时产品组列表中删除
        this.tempProducts = this.tempProducts.map(tempData => {
            if (tempData.groupIndex === groupIndex) {
                const updatedProducts = tempData.products.filter(product => product.id !== productId);
                return {
                    ...tempData,
                    products: updatedProducts
                };
            }
            return tempData;
        }).filter(tempData => tempData.products.length > 0); // 移除空的临时数据

        console.log('临时产品列表更新完成');
    }
    handleConfigureQuoteType(event) {
        this.showQuoteTypeModal = true;
    }
    

    
    // 删除报价方式 废弃
    // handleDeleteQuoteType(event) {
    //     const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
    //     const quoteType = event.currentTarget.dataset.quoteType;
    //     console.log(`删除报价方式，产品组索引: ${groupIndex}, 报价方式: ${quoteType}`);

    //     // 如果是在配置单产品
    //     if (this.currentConfigSingleIndex !== null) {
    //         this.handleDeleteSingleProductQuoteType(quoteType);
    //         return;
    //     }

    //     if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
    //         // 创建新的数组以确保响应式更新
    //         const updatedGroups = [...this.productGroups];
            
    //         if (quoteType === 'ladder') {
    //             // 虽然不再添加，但保留处理以防原有数据中存在
    //             updatedGroups[groupIndex].hasLadderQuote = false;
                
    //             // 检查是否还有其他使用阶梯的类型
    //             const hasOtherLadderTypes = 
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasSharedLadderUsagePriceDown ||
    //                 updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown;
                    
    //             // 只有当没有其他阶梯类型时才清空阶梯数据
    //             if (!hasOtherLadderTypes) {
    //             updatedGroups[groupIndex].tiers = [];
    //             }
    //         } else if (quoteType === 'minimumAmount') {
    //             // 虽然不再添加，但保留处理以防原有数据中存在
    //             updatedGroups[groupIndex].hasMinimumAmountQuote = false;
                
    //             // 检查是否还有其他使用保底金额的类型
    //             const hasOtherMinAmountTypes =
    //                 updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown;
                    
    //             // 只有当没有其他使用保底金额的类型时才清空保底金额数据
    //             if (!hasOtherMinAmountTypes) {
    //             updatedGroups[groupIndex].minimumAmount = null;
    //             }
    //         } else if (quoteType === 'minimumUnitPrice') {
    //             // 虽然不再添加，但保留处理以防原有数据中存在
    //             updatedGroups[groupIndex].hasMinimumUnitPriceQuote = false;
                
    //             // 检查是否还有其他使用保底单价*保底数量的类型
    //             const hasOtherMinUnitPriceTypes =
    //                 updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown;
                    
    //             // 只有当没有其他使用保底单价*保底数量的类型时才清空相关数据
    //             if (!hasOtherMinUnitPriceTypes) {
    //                 updatedGroups[groupIndex].minimumUnitPrice = null;
    //                 updatedGroups[groupIndex].minimumQuantity = null;
    //             }
    //         } else if (quoteType === 'fixedUsage') {
    //             // 虽然不再添加，但保留处理以防原有数据中存在
    //             updatedGroups[groupIndex].hasFixedUsageQuote = false;
    //             updatedGroups[groupIndex].fixedUsage = null;
    //             // 固定用量和固定金额现在是分开的，不再互相关联
    //         } else if (quoteType === 'fixedAmount') {
    //             // 处理固定金额
    //             updatedGroups[groupIndex].hasFixedAmountQuote = false;
    //             updatedGroups[groupIndex].fixedAmount = null;
    //             // 只清除固定金额相关数据
    //         } else if (quoteType === 'maximumAmount') {
    //             // 虽然不再添加，但保留处理以防原有数据中存在
    //             updatedGroups[groupIndex].hasMaximumAmountQuote = false;
    //             updatedGroups[groupIndex].maximumAmount = null;
    //         } else if (quoteType === 'productDiscount') {
    //             updatedGroups[groupIndex].hasProductDiscountQuote = false;
    //             updatedGroups[groupIndex].discountCoefficient = null;
    //             updatedGroups[groupIndex].fixedRebate = null;
    //             updatedGroups[groupIndex].cashReduce = null;
    //             updatedGroups[groupIndex].credit = null;
    //         } else if (quoteType === 'sharedLadderAmountDiscountZone') {
    //             // 只删除共享阶梯金额折扣分区，不影响其他阶梯类型
    //             updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone = false;
                
    //             // 检查是否还有其他使用阶梯的类型
    //             const hasOtherLadderTypes = 
    //                 updatedGroups[groupIndex].hasLadderQuote ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasSharedLadderUsagePriceDown ||
    //                 updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown;
                    
    //             // 只有当没有其他阶梯类型时才清空阶梯数据
    //             if (!hasOtherLadderTypes) {
    //                 updatedGroups[groupIndex].tiers = [];
    //             }
    //         } else if (quoteType === 'sharedLadderAmountDiscountDown') {
    //             // 只删除共享阶梯金额折扣落区，不影响其他阶梯类型
    //             updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown = false;
                
    //             // 检查是否还有其他使用阶梯的类型
    //             const hasOtherLadderTypes = 
    //                 updatedGroups[groupIndex].hasLadderQuote ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone ||
    //                 updatedGroups[groupIndex].hasSharedLadderUsagePriceDown ||
    //                 updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown;
                    
    //             // 只有当没有其他阶梯类型时才清空阶梯数据
    //             if (!hasOtherLadderTypes) {
    //                 updatedGroups[groupIndex].tiers = [];
    //             }
    //         } else if (quoteType === 'sharedLadderUsagePriceDown') {
    //             // 只删除共享阶梯用量单价落区，不影响其他阶梯类型
    //             updatedGroups[groupIndex].hasSharedLadderUsagePriceDown = false;
                
    //             // 检查是否还有其他使用阶梯的类型
    //             const hasOtherLadderTypes = 
    //                 updatedGroups[groupIndex].hasLadderQuote ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown;
                    
    //             // 只有当没有其他阶梯类型时才清空阶梯数据
    //             if (!hasOtherLadderTypes) {
    //                 updatedGroups[groupIndex].tiers = [];
    //             }
    //         } else if (quoteType === 'minAmountSharedLadderAmountDiscountDown') {
    //             updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown = false;
                
    //             // 同时移除对应的子类型标志
    //             if (!updatedGroups[groupIndex].hasMinimumAmountQuote) {
    //                 updatedGroups[groupIndex].minimumAmount = null;
    //             }
                
    //             // 检查是否还有使用保底金额的类型
    //             const hasOtherMinAmountTypes = updatedGroups[groupIndex].hasMinimumAmountQuote;
                
    //             // 只有当没有其他使用保底金额的类型时才清空保底金额
    //             if (!hasOtherMinAmountTypes) {
    //                 updatedGroups[groupIndex].minimumAmount = null;
    //             }
                
    //             // 检查是否还有其他使用阶梯的类型
    //             const hasOtherLadderTypes = 
    //                 updatedGroups[groupIndex].hasLadderQuote ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasSharedLadderUsagePriceDown ||
    //                 updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown;
                    
    //             // 只有当没有其他阶梯类型时才清空阶梯数据
    //             if (!hasOtherLadderTypes) {
    //                 updatedGroups[groupIndex].tiers = [];
    //             }
    //         } else if (quoteType === 'minUnitPriceSharedLadderUsagePriceDown') {
    //             updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown = false;
                
    //             // 检查是否还有使用保底单价*保底数量的类型
    //             const hasOtherMinUnitPriceTypes = updatedGroups[groupIndex].hasMinimumUnitPriceQuote;
                
    //             // 只有当没有其他使用保底单价*保底数量的类型时才清空相关数据
    //             if (!hasOtherMinUnitPriceTypes) {
    //                 updatedGroups[groupIndex].minimumUnitPrice = null;
    //                 updatedGroups[groupIndex].minimumQuantity = null;
    //             }
                
    //             // 检查是否还有其他使用阶梯的类型
    //             const hasOtherLadderTypes = 
    //                 updatedGroups[groupIndex].hasLadderQuote ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone ||
    //                 updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown ||
    //                 updatedGroups[groupIndex].hasSharedLadderUsagePriceDown ||
    //                 updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown;
                    
    //             // 只有当没有其他阶梯类型时才清空阶梯数据
    //             if (!hasOtherLadderTypes) {
    //                 updatedGroups[groupIndex].tiers = [];
    //             }
    //         }
            
    //         // 更新是否有任何报价方式的标志
    //         updatedGroups[groupIndex].hasAnyQuoteType = 
    //             updatedGroups[groupIndex].hasLadderQuote || 
    //             updatedGroups[groupIndex].hasMinimumAmountQuote ||
    //             updatedGroups[groupIndex].hasMinimumUnitPriceQuote ||
    //             updatedGroups[groupIndex].hasFixedUsageQuote ||
    //             updatedGroups[groupIndex].hasMaximumAmountQuote ||
    //             updatedGroups[groupIndex].hasProductDiscountQuote ||
    //             // 新增七种报价方式的判断
    //             updatedGroups[groupIndex].hasFixedAmountQuote ||
    //             updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone ||
    //             updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown ||
    //             updatedGroups[groupIndex].hasSharedLadderUsagePriceDown ||
    //             updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown ||
    //             updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown;
            
    //         this.productGroups = updatedGroups;
            
    //         // 显示成功提示
    //         this.dispatchEvent(
    //             new ShowToastEvent({
    //                 title: '成功',
    //                 message: '报价方式已删除',
    //                 variant: 'success'
    //             })
    //         );
    //     }
    // }
    
    // 保底金额变更处理
    handleMinimumAmountChange(event) {
        // 单产品弹窗
        if(this.currentConfigSingleIndex!==null){
            this.currentConfigGroup.minimumAmount = event.target.value;
            return;
        }
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        const value = event.target.value;
        console.log(`保底金额变更，产品组索引: ${groupIndex}, 值: ${value}`);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex].minimumAmount = value;

            // 如果是保底金额+共享阶梯金额折扣报价方式，同步更新第一个阶梯的下限
            const group = updatedGroups[groupIndex];
            if (group.hasMinAmountSharedLadderAmountDiscountDown && group.tiers && group.tiers.length > 0) {
                console.log('保底金额+共享阶梯金额折扣报价方式，同步更新第一个阶梯下限:', value);
                group.tiers[0].lowerBound = value || '0';
            }

            this.productGroups = updatedGroups;
        }
    }
    
    // 格式化单价显示 - 如果大于99四舍五入到两位小数并显示为6位小数，否则保持原样
    formatUnitPrice(value) {
        if (!value || value === '' || isNaN(value)) {
            return value;
        }

        const numValue = parseFloat(value);
        if (numValue > 99) {
            // 先四舍五入到两位小数，再格式化为6位小数显示
            const roundedValue = Math.round(numValue * 100) / 100;
            return roundedValue.toFixed(6);
        } else {
            return value; // 保持原样不变
        }
    }

    // 保底单价*保底数量变更处理
    handleMinimumUnitPriceChange(event) {
        // 单产品弹窗
        if(this.currentConfigSingleIndex!==null){
            const field=event.target.dataset.field;
            let value = event.target.value;

            // 如果是单价字段，进行格式化处理
            if (field === 'minimumUnitPrice') {
                value = this.formatUnitPrice(value);
                // 更新输入框显示值
                event.target.value = value;
            }

            this.currentConfigGroup[field] = value;

            // 如果是单价*数量+阶梯用量单价报价方式，且修改的是保底数量，自动更新第一个阶梯的上限
            if (field === 'minimumQuantity' && this.currentConfigGroup.hasMinUnitPriceSharedLadderUsagePriceDown) {
                if (this.currentConfigGroup.tiers && this.currentConfigGroup.tiers.length > 0) {
                    this.currentConfigGroup.tiers[0].upperBound = value;
                    console.log('单产品：自动更新第一个阶梯上限为保底数量:', value);
                }
            }
            return;
        }
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        const field = event.target.dataset.field;
        let value = event.target.value;

        // 如果是单价字段，进行格式化处理
        if (field === 'minimumUnitPrice') {
            value = this.formatUnitPrice(value);
            // 更新输入框显示值
            event.target.value = value;
        }

        console.log(`保底单价*保底数量变更，产品组索引: ${groupIndex}, 字段: ${field}, 值: ${value}`);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex][field] = value;

            // 如果是单价*数量+阶梯用量单价报价方式，且修改的是保底数量，自动更新第一个阶梯的上限
            if (field === 'minimumQuantity' && updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown) {
                if (updatedGroups[groupIndex].tiers && updatedGroups[groupIndex].tiers.length > 0) {
                    updatedGroups[groupIndex].tiers[0].upperBound = value;
                    console.log('产品组：自动更新第一个阶梯上限为保底数量:', value);
                }
            }

            this.productGroups = updatedGroups;
        }
    }

    // 是否赠送字段变更处理
    handleIsGiftChange(event) {
        const value = event.target.value;

        // 配置弹窗中的变更（单产品或产品组）
        if(this.currentConfigSingleIndex !== null || this.currentConfigGroupIndex !== null){
            this.currentConfigGroup.isGift = value;
            this.currentConfigGroup.isGiftLabel = value === '1' ? '是' : '否';
            console.log(`配置弹窗中是否赠送字段变更，值: ${value}`);
            return;
        }

        // 主界面中的变更（仅产品组）
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        console.log(`主界面是否赠送字段变更，产品组索引: ${groupIndex}, 值: ${value}`);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex].isGift = value;
            updatedGroups[groupIndex].isGiftLabel = value === '1' ? '是' : '否';
            this.productGroups = updatedGroups;
        }
    }

    
    // 固定用量*固定单价变更处理
    handleFixedUsageChange(event) {
        // 单产品弹窗
        if(this.currentConfigSingleIndex!==null){
            const field=event.target.dataset.field;
            this.currentConfigGroup[field]=event.target.value;
            return;
        }
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        const field = event.target.dataset.field;
        const value = event.target.value;
        console.log(`固定用量*固定单价变更，产品组索引: ${groupIndex}, 字段: ${field}, 值: ${value}`);
        
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex][field] = value;
            
            this.productGroups = updatedGroups;
        }
    }
    
    // 金额封顶变更处理
    handleMaximumAmountChange(event) {
        // 单产品弹窗
        if(this.currentConfigSingleIndex!==null){
            this.currentConfigGroup.maximumAmount = event.target.value;
            return;
        }
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        const value = event.target.value;
        console.log(`金额封顶变更，产品组索引: ${groupIndex}, 值: ${value}`);
        
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex].maximumAmount = value;
            
            this.productGroups = updatedGroups;
        }
    }
    
    // 产品折扣变更处理
    handleProductDiscountChange(event) {
        // 单产品弹窗
        if(this.currentConfigSingleIndex!==null){
            const field=event.target.dataset.field;
            this.currentConfigGroup[field]=event.target.value;
            return;
        }
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        const field = event.target.dataset.field;
        const value = event.target.value;
        console.log(`产品折扣变更，产品组索引: ${groupIndex}, 字段: ${field}, 值: ${value}`);
        
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex][field] = value;
            
            this.productGroups = updatedGroups;
        }
    }
    
    // 添加阶梯
    handleAddTier(event) {
        // 单产品弹窗
        if(this.currentConfigSingleIndex!==null){
            const newTier = this.createEmptyTier();
            const list = Array.isArray(this.currentConfigGroup.tiers) ? [...this.currentConfigGroup.tiers] : [];

            // 检查当前单产品的报价方式类型
            const isSharedLadderAmountDiscountDown = this.currentConfigGroup.hasSharedLadderAmountDiscountDown ||
                                                   this.currentConfigGroup.hasMinAmountSharedLadderAmountDiscountDown;
            const isSharedLadderUsagePriceDown = this.currentConfigGroup.hasSharedLadderUsagePriceDown ||
                                               this.currentConfigGroup.hasMinUnitPriceSharedLadderUsagePriceDown;
            const isSharedLadderAmountDiscountZone = this.currentConfigGroup.hasSharedLadderAmountDiscountZone;

            // 根据报价方式类型设置单位和计算方式
            if (isSharedLadderAmountDiscountZone) {
                newTier.unit = '金额';
                newTier.calculationMethod = '分区';
                newTier.unitNotEditable = true;
                newTier.calculationMethodNotEditable = true;

                // 设置默认值
                if (list.length > 0) {
                    const lastTier = list[list.length - 1];
                    newTier.lowerBound = lastTier.upperBound;
                    newTier.upperBound = (parseInt(lastTier.upperBound) * 2).toString();
                    newTier.discount = lastTier.discount;
                } else {
                    newTier.lowerBound = '0';
                    newTier.upperBound = '100000';
                    newTier.discount = '90';
                }
            } else if (isSharedLadderAmountDiscountDown) {
                newTier.unit = '金额';
                newTier.calculationMethod = '落区';
                newTier.unitNotEditable = true;
                newTier.calculationMethodNotEditable = true;

                // 设置默认值
                if (list.length > 0) {
                    const lastTier = list[list.length - 1];
                    newTier.lowerBound = lastTier.upperBound;
                    newTier.upperBound = (parseInt(lastTier.upperBound) * 2).toString();
                    newTier.discount = lastTier.discount;
                } else {
                    newTier.lowerBound = '0';
                    newTier.upperBound = '100000';
                    newTier.discount = '90';
                }
            } else if (isSharedLadderUsagePriceDown) {
                newTier.unit = '用量';
                newTier.calculationMethod = '落区';
                newTier.unitNotEditable = true;
                newTier.calculationMethodNotEditable = true;

                // 设置默认值
                if (list.length > 0) {
                    const lastTier = list[list.length - 1];
                    newTier.lowerBound = lastTier.upperBound;
                    newTier.upperBound = (parseInt(lastTier.upperBound) * 2).toString();
                    newTier.discount = lastTier.discount;
                } else {
                    newTier.lowerBound = '0';
                    newTier.upperBound = '100000';
                    newTier.discount = '90';
                }
            } else {
                // 默认设置
                newTier.unit = '用量';
                newTier.calculationMethod = '分区';
            }

            // 如果有阶梯，先将所有阶梯设为不可删除
            if (list.length > 0) {
                list.forEach(tier => {
                    tier.isDeletable = false;
                });
            }

            // 将新阶梯设置为可删除
            newTier.isDeletable = true;

            list.push(newTier);
            this.currentConfigGroup.tiers = list;

            console.log('单产品添加阶梯完成，当前阶梯数量:', list.length);
            return;
        }
        const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
        console.log('添加阶梯，产品组索引:', groupIndex);
        
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            const group = this.productGroups[groupIndex];
            
            // 检查当前报价方式
            const isSharedLadderAmountDiscountDown = group.hasSharedLadderAmountDiscountDown || 
                                                   group.hasMinAmountSharedLadderAmountDiscountDown;
            const isSharedLadderUsagePriceDown = group.hasSharedLadderUsagePriceDown ||
                                               group.hasMinUnitPriceSharedLadderUsagePriceDown;
            const isSharedLadderAmountDiscountZone = group.hasSharedLadderAmountDiscountZone;
            
            // 创建新的阶梯
            const newTier = this.createEmptyTier();
            
            // 根据报价方式类型设置单位和计算方式
            if (isSharedLadderAmountDiscountZone) {
                newTier.unit = '金额';
                newTier.calculationMethod = '分区';
                newTier.unitNotEditable = true; // 单位不可编辑
                newTier.calculationMethodNotEditable = true; // 计算方式不可编辑
                
                // 设置默认值
                if (group.tiers.length > 0) {
                    const lastTier = group.tiers[group.tiers.length - 1];
                    newTier.lowerBound = lastTier.upperBound;
                    newTier.upperBound = (parseInt(lastTier.upperBound) * 2).toString();
                    newTier.discount = lastTier.discount;
                } else {
                    newTier.lowerBound = '0';
                    newTier.upperBound = '100000';
                    newTier.discount = '90';
                }
            } else if (isSharedLadderAmountDiscountDown) {
                newTier.unit = '金额';
                newTier.calculationMethod = '落区';
                newTier.unitNotEditable = true; // 单位不可编辑
                newTier.calculationMethodNotEditable = true; // 计算方式不可编辑
                
                // 设置默认值
                if (group.tiers.length > 0) {
                    const lastTier = group.tiers[group.tiers.length - 1];
                    newTier.lowerBound = lastTier.upperBound;
                    newTier.upperBound = (parseInt(lastTier.upperBound) * 2).toString();
                    newTier.discount = lastTier.discount;
                } else {
                    // 对于保底金额+共享阶梯金额折扣，第一个阶梯的下限使用保底金额
                    if (group.hasMinAmountSharedLadderAmountDiscountDown) {
                        newTier.lowerBound = group.minimumAmount || '0';
                    } else {
                        newTier.lowerBound = '0';
                    }
                    newTier.upperBound = '100000';
                    newTier.discount = '90';
                }
            } else if (isSharedLadderUsagePriceDown) {
                newTier.unit = '用量';
                newTier.calculationMethod = '落区';
                newTier.unitNotEditable = true; // 单位不可编辑
                newTier.calculationMethodNotEditable = true; // 计算方式不可编辑
                
                // 设置默认值
                if (group.tiers.length > 0) {
                    const lastTier = group.tiers[group.tiers.length - 1];
                    newTier.lowerBound = lastTier.upperBound;
                    newTier.upperBound = (parseInt(lastTier.upperBound) * 2).toString();
                    newTier.discount = lastTier.discount;
                } else {
                    newTier.lowerBound = '0';
                    // 对于单价*数量+阶梯用量单价，第一个阶梯的上限使用保底数量
                    if (group.hasMinUnitPriceSharedLadderUsagePriceDown) {
                        newTier.upperBound = group.minimumQuantity || '100000';
                    } else {
                        newTier.upperBound = '100000';
                    }
                    newTier.discount = '90';
                }
            } else {
                // 默认设置
                newTier.unit = '用量';
                newTier.calculationMethod = '分区';
            }
            
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 如果有阶梯，先将所有阶梯设为不可删除
            if (updatedGroups[groupIndex].tiers.length > 0) {
                updatedGroups[groupIndex].tiers.forEach(tier => {
                    tier.isDeletable = false;
                });
            }
            
            // 将新阶梯设置为可删除
            newTier.isDeletable = true;
            
            updatedGroups[groupIndex].tiers = [...updatedGroups[groupIndex].tiers, newTier];
            
            this.productGroups = updatedGroups;
            console.log('阶梯添加完成，当前阶梯数量:', this.productGroups[groupIndex].tiers.length);
        }
    }
    
    // 删除阶梯
    handleDeleteTier(event) {
        // 单产品弹窗
        if (this.currentConfigSingleIndex !== null) {
            const tierIndex = parseInt(event.currentTarget.dataset.tierIndex, 10);
            console.log(`删除单产品阶梯，阶梯索引: ${tierIndex}`);

            const tiers = Array.isArray(this.currentConfigGroup.tiers) ? this.currentConfigGroup.tiers : [];

            if (tierIndex >= 0 && tierIndex < tiers.length) {
                // 检查是否可以删除该阶梯（只能从下往上删除）
                const tier = tiers[tierIndex];
                if (!tier.isDeletable) {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: '提示',
                            message: '只能从下往上删除阶梯行，请先删除下面的阶梯行',
                            variant: 'warning'
                        })
                    );
                    return;
                }

                // 删除指定的阶梯
                const updatedTiers = tiers.filter((_, index) => index !== tierIndex);

                // 更新删除后的最后一个阶梯为可删除
                if (updatedTiers.length > 0) {
                    const lastTier = updatedTiers[updatedTiers.length - 1];
                    lastTier.isDeletable = true;
                }

                this.currentConfigGroup.tiers = updatedTiers;
                console.log('单产品阶梯删除完成，剩余阶梯数量:', updatedTiers.length);
            }
            return;
        }

        // 产品组逻辑
        const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
        const tierIndex = parseInt(event.currentTarget.dataset.tierIndex, 10);
        console.log(`删除阶梯，产品组索引: ${groupIndex}, 阶梯索引: ${tierIndex}`);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length &&
            tierIndex >= 0 && tierIndex < this.productGroups[groupIndex].tiers.length) {

            // 检查是否可以删除该阶梯（只能从下往上删除）
            const tier = this.productGroups[groupIndex].tiers[tierIndex];
            if (!tier.isDeletable) {
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: '提示',
                        message: '只能从下往上删除阶梯行，请先删除下面的阶梯行',
                        variant: 'warning'
                    })
                );
                return;
            }

            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex].tiers = updatedGroups[groupIndex].tiers.filter((_, index) => index !== tierIndex);

            // 更新删除后的最后一个阶梯为可删除
            if (updatedGroups[groupIndex].tiers.length > 0) {
                const lastTier = updatedGroups[groupIndex].tiers[updatedGroups[groupIndex].tiers.length - 1];
                lastTier.isDeletable = true;
            }

            this.productGroups = updatedGroups;
            console.log('阶梯删除完成，剩余阶梯数量:', this.productGroups[groupIndex].tiers.length);
        }
    }
    
    // 处理产品选择变更
    handleProductChange(event) {
        try {
            const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
            const productIndex = parseInt(event.target.dataset.productIndex, 10);
            const productId = event.detail.value && event.detail.value.length > 0 ? event.detail.value[0] : null;
            
            console.log(`产品变更，产品组索引: ${groupIndex}, 产品索引: ${productIndex}, 新产品ID: ${productId}`);
            
            if (groupIndex >= 0 && groupIndex < this.productGroups.length &&
                productIndex >= 0 && productIndex < this.productGroups[groupIndex].products.length) {
                
                // 检查产品重复逻辑
                if (productId) {
                    // 检查当前产品组内是否已存在该产品
                    const isDuplicateInGroup = this.productGroups[groupIndex].products.some(
                        (product, idx) => idx !== productIndex && product.productId === productId
                    );
                    
                    if (isDuplicateInGroup) {
                        this.dispatchEvent(
                            new ShowToastEvent({
                                title: '错误',
                                message: '该产品已在当前产品组中添加，不能添加重复的产品',
                                variant: 'error'
                            })
                        );
                        
                        // 清空当前选择
                        const input = this.template.querySelector(`lightning-input-field[data-group-index="${groupIndex}"][data-product-index="${productIndex}"]`);
                        if (input) {
                            input.reset();
                        }
                        return;
                    }
                }
                
                // 创建新的数组以确保响应式更新
                const updatedGroups = [...this.productGroups];
                
                if (productId) {
                    updatedGroups[groupIndex].products[productIndex].productId = productId;
                    
                    // 更新所有产品字段
                    if (event.detail.value[0] && event.detail.value[0].fields) {
                        const fields = event.detail.value[0].fields;
                        
                        // 更新产品名称
                        if (fields.Name) {
                            updatedGroups[groupIndex].products[productIndex].productName = fields.Name.value;
                            updatedGroups[groupIndex].products[productIndex].Name = fields.Name.value;
                        }
                        
                        // 更新产品代码
                        if (fields.ProductCode) {
                            updatedGroups[groupIndex].products[productIndex].ProductCode = fields.ProductCode.value;
                        }
                        
                        // 更新产品系列
                        if (fields.Family) {
                            updatedGroups[groupIndex].products[productIndex].Family = fields.Family.value;
                        }
                        
                        // 更新产品描述
                        if (fields.Description) {
                            updatedGroups[groupIndex].products[productIndex].Description = fields.Description.value;
                        }
                        
                        // 更新计量单位
                        if (fields.QuantityUnitOfMeasure) {
                            updatedGroups[groupIndex].products[productIndex].QuantityUnitOfMeasure = fields.QuantityUnitOfMeasure.value;
                        }
                        
                        // 更新区域
                        if ('Region__c' in fields) {
                            updatedGroups[groupIndex].products[productIndex].Region = fields.Region__c.value;
                        }

                        // 更新是否一次性费用
                        if ('IS_OneTimeFee__c' in fields) {
                            updatedGroups[groupIndex].products[productIndex].OneTimeFee = fields.IS_OneTimeFee__c.value;
                        }

                        // 更新计费类型
                        if ('Charge_Type__c' in fields) {
                            updatedGroups[groupIndex].products[productIndex].chargeType = fields.Charge_Type__c.value;
                        }

                        console.log('更新产品信息为:', updatedGroups[groupIndex].products[productIndex]);
                    }
                } else {
                    updatedGroups[groupIndex].products[productIndex].productId = '';
                    updatedGroups[groupIndex].products[productIndex].productName = '未知产品';
                    updatedGroups[groupIndex].products[productIndex].ProductCode = '';
                    updatedGroups[groupIndex].products[productIndex].Name = '';
                    updatedGroups[groupIndex].products[productIndex].Family = '';
                    updatedGroups[groupIndex].products[productIndex].Description = '';
                    updatedGroups[groupIndex].products[productIndex].QuantityUnitOfMeasure = '';
                    updatedGroups[groupIndex].products[productIndex].Region = '';
                    updatedGroups[groupIndex].products[productIndex].OneTimeFee = '否';
                    updatedGroups[groupIndex].products[productIndex].chargeType = '计费';
                }
                
                this.productGroups = updatedGroups;
                console.log('产品选择更新完成');
            }
        } catch (error) {
            console.error('处理产品选择变更时出错:', error);
        }
    }
    
    // 处理产品字段变更
    handleFieldChange(event) {
        try {
            const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
            const productIndex = parseInt(event.target.dataset.productIndex, 10);
            const fieldName = event.target.dataset.field;
            const value = event.target.value;
            
            console.log(`字段变更，产品组索引: ${groupIndex}, 产品索引: ${productIndex}, 字段: ${fieldName}, 值: ${value}`);
            
            if (groupIndex >= 0 && groupIndex < this.productGroups.length &&
                productIndex >= 0 && productIndex < this.productGroups[groupIndex].products.length &&
                fieldName) {
                
                // 创建新的数组以确保响应式更新
                const updatedGroups = [...this.productGroups];
                updatedGroups[groupIndex].products[productIndex][fieldName] = value;
                
                this.productGroups = updatedGroups;
                console.log('字段更新完成');
            }
        } catch (error) {
            console.error('处理字段变更时出错:', error);
        }
    }
    
    // 判断阶梯行是否可以删除（只有最后一行可以删除）
    canDeleteTier(groupIndex, tierIndex) {
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            const tiers = this.productGroups[groupIndex].tiers;
            // 只有最后一行可以删除
            return tierIndex === tiers.length - 1;
        }
        return false;
    }
    
    // 处理阶梯字段变更
    handleTierChange(event) {
        try {
            // 单产品弹窗
            if(this.currentConfigSingleIndex!==null){
                const tierIndex=parseInt(event.target.dataset.tierIndex,10);
                const field=event.target.dataset.field;
                let value = event.target.value;

                // 如果是阶梯用量单价字段，进行格式化处理
                if (field === 'discount') {
                    value = this.formatUnitPrice(value);
                    // 更新输入框显示值
                    event.target.value = value;
                }

                if(Array.isArray(this.currentConfigGroup.tiers)&&tierIndex>=0&&tierIndex<this.currentConfigGroup.tiers.length){
                    this.currentConfigGroup.tiers[tierIndex][field]=value;
                }
                return;
            }
            const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
            const tierIndex = parseInt(event.target.dataset.tierIndex, 10);
            const fieldName = event.target.dataset.field;
            let value = event.target.value;

            // 如果是阶梯用量单价字段，进行格式化处理
            if (fieldName === 'discount') {
                value = this.formatUnitPrice(value);
                // 更新输入框显示值
                event.target.value = value;
            }
            
            console.log(`阶梯字段变更，产品组索引: ${groupIndex}, 阶梯索引: ${tierIndex}, 字段: ${fieldName}, 值: ${value}`);
            
            if (groupIndex >= 0 && groupIndex < this.productGroups.length &&
                tierIndex >= 0 && tierIndex < this.productGroups[groupIndex].tiers.length &&
                fieldName) {
                
                // 检查是否可编辑
                const group = this.productGroups[groupIndex];
                const tier = this.productGroups[groupIndex].tiers[tierIndex];
                
                // 检查报价方式类型
                const isSharedLadderAmountDiscountDown = group.hasSharedLadderAmountDiscountDown || 
                                                       group.hasMinAmountSharedLadderAmountDiscountDown;
                const isSharedLadderUsagePriceDown = group.hasSharedLadderUsagePriceDown ||
                                                   group.hasMinUnitPriceSharedLadderUsagePriceDown;
                const isSharedLadderAmountDiscountZone = group.hasSharedLadderAmountDiscountZone;
                
                // 如果是特定报价方式且尝试修改单位或计算方式，则不允许修改
                if ((fieldName === 'unit' && tier.unitNotEditable) || 
                    (fieldName === 'calculationMethod' && tier.calculationMethodNotEditable)) {
                    
                    let reportedType = '';
                    if (isSharedLadderAmountDiscountDown) {
                        reportedType = '共享阶梯金额折扣落区';
                        if (group.hasMinAmountSharedLadderAmountDiscountDown) {
                            reportedType = '保底金额+共享阶梯金额折扣';
                        }
                    } else if (isSharedLadderUsagePriceDown) {
                        reportedType = '阶梯用量单价';
                        if (group.hasMinUnitPriceSharedLadderUsagePriceDown) {
                            reportedType = '单价*数量+阶梯用量单价';
                        }
                    } else if (isSharedLadderAmountDiscountZone) {
                        reportedType = '阶梯金额折扣';
                    }
                    
                    console.log(`${reportedType}的单位和计算方式不可编辑`);
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: '提示',
                            message: `${reportedType}的单位和计算方式不可编辑`,
                            variant: 'warning'
                        })
                    );
                    
                    // 重置输入值为原始值
                    const input = event.target;
                    if (input) {
                        setTimeout(() => {
                            input.value = tier[fieldName];
                        }, 0);
                    }
                    
                    return;
                }
                
                // 创建新的数组以确保响应式更新
                const updatedGroups = [...this.productGroups];
                updatedGroups[groupIndex].tiers[tierIndex][fieldName] = value;
                
                // 如果修改了上限或下限，确保上限大于下限（如果上限不为空）
                if (fieldName === 'lowerBound' || fieldName === 'upperBound') {
                    const lowerBound = fieldName === 'lowerBound' ?
                        parseFloat(value) :
                        parseFloat(updatedGroups[groupIndex].tiers[tierIndex].lowerBound);
                    const upperBound = fieldName === 'upperBound' ?
                        parseFloat(value) :
                        parseFloat(updatedGroups[groupIndex].tiers[tierIndex].upperBound);

                    // 只有当上限不为空时才进行比较
                    const upperBoundValue = fieldName === 'upperBound' ? value : updatedGroups[groupIndex].tiers[tierIndex].upperBound;
                    if (upperBoundValue !== '' && upperBoundValue !== null && upperBoundValue !== undefined) {
                        if (!isNaN(lowerBound) && !isNaN(upperBound) && lowerBound >= upperBound) {
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    title: '警告',
                                    message: '阶梯上限必须大于下限',
                                    variant: 'warning'
                                })
                            );
                        }
                    }
                    
                    // 检查当前阶梯的上限是否小于下一个阶梯的下限（如果上限不为空）
                    if (fieldName === 'upperBound' && tierIndex < updatedGroups[groupIndex].tiers.length - 1) {
                        const upperBoundValue = value;
                        if (upperBoundValue !== '' && upperBoundValue !== null && upperBoundValue !== undefined) {
                            const nextTierLowerBound = parseFloat(updatedGroups[groupIndex].tiers[tierIndex + 1].lowerBound);
                            if (!isNaN(upperBound) && !isNaN(nextTierLowerBound) && upperBound > nextTierLowerBound) {
                                this.dispatchEvent(
                                    new ShowToastEvent({
                                        title: '警告',
                                        message: '阶梯上限不能大于下一个阶梯的下限',
                                        variant: 'warning'
                                    })
                                );
                            }
                        }
                    }
                    
                    // 检查当前阶梯的下限是否小于上一个阶梯的上限（如果上一个阶梯的上限不为空）
                    if (fieldName === 'lowerBound' && tierIndex > 0) {
                        const prevTierUpperBoundValue = updatedGroups[groupIndex].tiers[tierIndex - 1].upperBound;
                        if (prevTierUpperBoundValue !== '' && prevTierUpperBoundValue !== null && prevTierUpperBoundValue !== undefined) {
                            const prevTierUpperBound = parseFloat(prevTierUpperBoundValue);
                            if (!isNaN(lowerBound) && !isNaN(prevTierUpperBound) && lowerBound < prevTierUpperBound) {
                                this.dispatchEvent(
                                    new ShowToastEvent({
                                        title: '警告',
                                        message: '阶梯下限不能小于上一个阶梯的上限',
                                        variant: 'warning'
                                    })
                                );
                            }
                        }
                    }
                }
                
                this.productGroups = updatedGroups;
                console.log('阶梯字段更新完成');
            }
        } catch (error) {
            console.error('处理阶梯字段变更时出错:', error);
        }
    }
    

    
    // 提交数据
    handleSubmit() {
        console.log('提交数据');

        if (!this.validateData()) {
            console.log('数据验证失败');
            return;
        }


        // 首先保存临时产品到数据库
        this.saveTempProducts()
            .then(() => {
                // 准备提交的数据
                const dataToSave = this.prepareDataForSave();
                console.log('准备提交的数据:', JSON.stringify(dataToSave));

                // 准备单产品数据
                const singleProductsToSave = this.prepareSingleProductsForSave();
                console.log('准备提交的单产品数据:', JSON.stringify(singleProductsToSave));

                // 根据是否有数据准备Promise列表
                const promises = [];

                // 始终调用saveProductGroupQuote，即使数据为空，以确保后端清理
                promises.push(
                    saveProductGroupQuote({
                        productGroupsJSON: JSON.stringify(dataToSave),
                        quoteId: this.recordId
                    })
                );

                // 始终调用saveSingleProductQuote，即使数据为空，以确保后端清理
                promises.push(
                    saveSingleProductQuote({
                        singleProductsJSON: JSON.stringify(singleProductsToSave),
                        quoteId: this.recordId
                    })
                );

                return Promise.all(promises);
            })
            .then(results => {
                console.log('保存成功:', JSON.stringify(results));

                // 检查所有结果是否成功
                const allSuccess = results.every(result => result.isSuccess === 'true');

                if (allSuccess) {
                    // 清空临时产品列表
                    this.tempProducts = [];
                    this.tempSingleProducts = [];
                    this.isSubmitting = true;

                    this.isEditMode = false;
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: '成功',
                            message: '产品数据已保存',
                            variant: 'success'
                        })
                    );
                    // 重新加载数据
                    this.loadProductGroups();
                    this.loadSingleProducts();

                    // 计算并更新合同收入
                    this.calculateContractRevenue();

                    this.isSubmitting = false;
                    this[NavigationMixin.Navigate]({
                        type: 'standard__recordPage',
                        attributes: {
                            recordId: this.recordId,
                            actionName: 'view'
                        }
                    });
                } else {
                    // 收集所有错误消息
                    const errorMessages = results
                        .filter(result => result.isSuccess !== 'true')
                        .map(result => result.msg)
                        .join('; ');

                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: '失败',
                            message: errorMessages,
                            variant: 'error'
                        })
                    );
                }
               

                
            })
            .catch(error => {
                console.error('保存产品数据失败:', this.reduceErrors(error));
                this.showError('保存产品数据失败: ' + this.reduceErrors(error));
            })
            // .finally(() => {
                
            // });
    }

    // 保存临时产品到数据库
    async saveTempProducts() {
        console.log('保存临时产品到数据库');

        // 保存临时产品组产品
        // for (const tempProductData of this.tempProducts) {
        //     try {
        //         const result = await handleProductInfoProducts({
        //             productsJSON: JSON.stringify(tempProductData.products),
        //             quoteId: this.recordId,
        //             productGroupId: tempProductData.productGroupId
        //         });
        //         console.log('保存临时产品组产品成功:', result);
        //     } catch (error) {
        //         console.error('保存临时产品组产品失败:', this.reduceErrors(error));
        //         throw error;
        //     }
        // }

        // // 保存临时单产品
        // for (const tempSingleProductData of this.tempSingleProducts) {
        //     try {
        //         const result = await handleProductInfoProducts({
        //             productsJSON: JSON.stringify(tempSingleProductData.products),
        //             quoteId: this.recordId,
        //             productGroupId: 'single'
        //         });
        //         console.log('保存临时单产品成功:', result);
        //     } catch (error) {
        //         console.error('保存临时单产品失败:', this.reduceErrors(error));
        //         throw error;
        //     }
        // }

        console.log('所有临时产品保存完成');
    }
    
    // 准备单产品数据保存
    prepareSingleProductsForSave() {
        // 过滤掉无效的产品（没有productId或Name的产品）
        // const validProducts = this.singleProducts.filter(product => {
        //     const hasValidProductId = product.productId && product.productId !== 'null' && product.productId !== '';
        //     const hasValidName = product.Name && product.Name !== 'null' && product.Name !== '';
        //     const isValid = hasValidProductId && hasValidName;

        //     if (!isValid) {
        //         console.log('过滤掉无效的单产品:', product.id, 'productId:', product.productId, 'Name:', product.Name);
        //     }

        //     return isValid;
        // });

        // return validProducts.map(product => {
        return this.singleProducts.map(product => {

            // 检查是否是组合报价方式
            const isMinAmountSharedLadder = product.hasMinAmountSharedLadderAmountDiscountDown === true;
            const isMinUnitPriceSharedLadder = product.hasMinUnitPriceSharedLadderUsagePriceDown === true;
            
            // 确保组合报价方式的保底数据不为空
            let minimumAmount = product.minimumAmount;
            let minimumUnitPrice = product.minimumUnitPrice;
            let minimumQuantity = product.minimumQuantity;
            
            if (isMinAmountSharedLadder && (!minimumAmount || minimumAmount === 'null')) {
                minimumAmount = '0';
            }
            
            if (isMinUnitPriceSharedLadder) {
                if (!minimumUnitPrice || minimumUnitPrice === 'null') {
                    minimumUnitPrice = '0';
                }
                if (!minimumQuantity || minimumQuantity === 'null') {
                    minimumQuantity = '0';
                }
            }

            return {
                id: null,
                productId: product.productId,
                ProductCode: product.ProductCode,
                Name: product.Name,
                Family: product.Family,
                Description: product.Description,
                ProductDescription: product.ProductDescription,
                QuantityUnitOfMeasure: product.QuantityUnitOfMeasure,
                taxRate: product.taxRate,
                unitPrice: product.unitPrice,
                listPrice: product.listPrice,
                listPriceCurrency: product.listPriceCurrency,
                pricingId: product.pricingId, // 添加pricingId字段
                customerAccountId: product.customerAccountId,
                profitDescription: product.profitDescription,
                ChargeExplanation: product.ChargeExplanation,
                QuoteLineStartDate: product.QuoteLineStartDate,
                QuoteLineEndDate: product.QuoteLineEndDate,
                Region: product.Region,
                OneTimeFee: product.OneTimeFee,
                chargeType: product.chargeType,
                quoteTypeValue: product.quoteTypeValue,
                // 报价方式标志
                hasFixedAmountQuote: product.hasFixedAmountQuote,
                hasProductDiscountQuote: product.hasProductDiscountQuote,
                hasMinimumUnitPriceQuote: product.hasMinimumUnitPriceQuote,
                hasSharedLadderAmountDiscountZone: product.hasSharedLadderAmountDiscountZone,
                hasSharedLadderAmountDiscountDown: product.hasSharedLadderAmountDiscountDown,
                hasSharedLadderUsagePriceDown: product.hasSharedLadderUsagePriceDown,
                hasMinAmountSharedLadderAmountDiscountDown: product.hasMinAmountSharedLadderAmountDiscountDown,
                hasMinUnitPriceSharedLadderUsagePriceDown: product.hasMinUnitPriceSharedLadderUsagePriceDown,
                // 报价方式相关数据
                fixedUsage: product.fixedUsage,
                fixedAmount: product.fixedAmount,
                discountCoefficient: product.discountCoefficient,
                fixedRebate: product.fixedRebate,
                cashReduce: product.cashReduce,
                credit: product.credit,
                discountType: product.discountType,
                ladderType:product.ladderType,
                // 组合报价方式相关数据
                minimumAmount: minimumAmount,
                minimumGuaranteeType: product.minimumGuaranteeType || '1', // 保底类型，默认为折前
                minimumUnitPrice: minimumUnitPrice,
                minimumQuantity: minimumQuantity,
                expandMultiple: product.expandMultiple, // 扩大倍数字段
                isGift: product.isGift, // 是否赠送字段
                // 阶梯数据
                tiers: product.tiers || [],
                // 单产品特有标识
                isGroup: false,  // 标记为单产品，非产品组中的产品
                exchangeRateDifferenceLabel: this.exchangeRateDifference,
                exchangeRateDifferenceValue: this.quoteInfo?.exchangeRateDifferenceValue || '2'
            };
        });
    }
    
    // 准备要保存的数据
    prepareDataForSave() {
        return this.productGroups.map(group => {
            // 检查是否是组合报价方式，如果是，需要确保相应的基础报价方式标志也被设置
            const isMinAmountSharedLadder = group.hasMinAmountSharedLadderAmountDiscountDown === true;
            const isMinUnitPriceSharedLadder = group.hasMinUnitPriceSharedLadderUsagePriceDown === true;
            
            // 如果是保底金额+共享阶梯金额折扣落区，确保hasMinimumAmountQuote也为true
            const hasMinimumAmountQuote = isMinAmountSharedLadder || group.hasMinimumAmountQuote === true;
            
            // 如果是单价*数量+共享阶梯用量单价落区，确保hasMinimumUnitPriceQuote也为true
            const hasMinimumUnitPriceQuote = isMinUnitPriceSharedLadder || group.hasMinimumUnitPriceQuote === true;
            
            // 确保组合报价方式的保底数据正确保存
            let minimumAmount = group.minimumAmount;
            let minimumUnitPrice = group.minimumUnitPrice;
            let minimumQuantity = group.minimumQuantity;
            
            // 确保组合报价方式的保底数据不为空
            if (isMinAmountSharedLadder && (!minimumAmount || minimumAmount === 'null')) {
                console.log('保底金额+共享阶梯金额折扣落区的保底金额为空，设置默认值');
                minimumAmount = '0'; // 设置默认值
            }
            
            if (isMinUnitPriceSharedLadder) {
                if (!minimumUnitPrice || minimumUnitPrice === 'null') {
                    console.log('单价*数量+阶梯用量单价的保底单价为空，设置默认值');
                    minimumUnitPrice = '0'; // 设置默认值
                }
                if (!minimumQuantity || minimumQuantity === 'null') {
                    console.log('单价*数量+阶梯用量单价的保底数量为空，设置默认值');
                    minimumQuantity = '0'; // 设置默认值
                }
            }
            
            console.log('组合报价方式状态:', {
                hasMinAmountSharedLadderAmountDiscountDown: isMinAmountSharedLadder,
                hasMinUnitPriceSharedLadderUsagePriceDown: isMinUnitPriceSharedLadder,
                hasMinimumAmountQuote: hasMinimumAmountQuote,
                hasMinimumUnitPriceQuote: hasMinimumUnitPriceQuote,
                minimumAmount: minimumAmount,
                minimumUnitPrice: minimumUnitPrice,
                minimumQuantity: minimumQuantity
            });
            
            // 处理阶梯数据，确保单位和计算方式正确
            const tiers = Array.isArray(group.tiers) ? group.tiers.map(tier => {
                let unit = tier.unit;
                let calculationMethod = tier.calculationMethod;
                
                // 根据报价方式类型设置单位和计算方式
                if (isMinAmountSharedLadder || group.hasSharedLadderAmountDiscountDown) {
                    unit = '金额';
                    calculationMethod = '落区';
                } else if (isMinUnitPriceSharedLadder || group.hasSharedLadderUsagePriceDown) {
                    unit = '用量';
                    calculationMethod = '落区';
                } else if (group.hasSharedLadderAmountDiscountZone) {
                    unit = '金额';
                    calculationMethod = '分区';
                }
                
                return {
                    id: tier.id && !tier.id.startsWith('temp_') ? tier.id : null,
                    lowerBound: tier.lowerBound,
                    upperBound: tier.upperBound,
                    unit: unit,
                    discount: tier.discount,
                    calculationMethod: calculationMethod
                };
            }) : [];
            
            return {
                id: group.id && !group.id.startsWith('temp_') ? group.id : null,
                groupNumber: group.groupNumber,
                products: group.products.map(product => {
                    return {
                        id: product.id && !product.id.startsWith('temp_') ? product.id : null,
                        productId: product.productId,
                        ProductCode: product.ProductCode,
                        Name: product.Name,
                        Family: product.Family,
                        Description: product.Description,
                        ProductDescription: product.ProductDescription,
                        QuantityUnitOfMeasure: product.QuantityUnitOfMeasure,
                        taxRate: product.taxRate,
                        unitPrice: product.unitPrice,
                        listPrice: product.listPrice,
                        listPriceCurrency: product.listPriceCurrency,
                        pricingId: product.pricingId, // 添加pricingId字段
                        customerAccountId: product.customerAccountId,
                        profitDescription: product.profitDescription,
                        ChargeExplanation: product.ChargeExplanation,
                        QuoteLineStartDate: product.QuoteLineStartDate,
                        QuoteLineEndDate: product.QuoteLineEndDate,
                        isGroup: true,  // 标记为产品组中的产品
                        Region: product.Region,
                        OneTimeFee: product.OneTimeFee,
                        chargeType: product.chargeType,
                        exchangeRateDifferenceLabel: this.exchangeRateDifference,
                        exchangeRateDifferenceValue: this.quoteInfo?.exchangeRateDifferenceValue || '2'
                    };
                }),
                tiers: tiers,
                // 当有任何阶梯报价方式或存在阶梯数据时，将hasLadderQuote设置为true
                hasLadderQuote: group.hasSharedLadderAmountDiscountZone === true || 
                              group.hasSharedLadderAmountDiscountDown === true || 
                              group.hasSharedLadderUsagePriceDown === true || 
                              isMinAmountSharedLadder === true || 
                              isMinUnitPriceSharedLadder === true ||
                              tiers.length > 0,
                // 修改这里，使用上面计算的组合值
                hasMinimumAmountQuote: hasMinimumAmountQuote,
                hasMinimumUnitPriceQuote: hasMinimumUnitPriceQuote,
                hasFixedUsageQuote: false,
                hasMaximumAmountQuote: false,
                
                // 保留的报价方式
                hasProductDiscountQuote: group.hasProductDiscountQuote,
                
                // 明确设置组合报价方式标志
                hasMinAmountSharedLadderAmountDiscountDown: isMinAmountSharedLadder,
                hasMinUnitPriceSharedLadderUsagePriceDown: isMinUnitPriceSharedLadder,
                hasSharedLadderAmountDiscountZone: group.hasSharedLadderAmountDiscountZone,
                hasSharedLadderAmountDiscountDown: group.hasSharedLadderAmountDiscountDown,
                hasSharedLadderUsagePriceDown: group.hasSharedLadderUsagePriceDown,
                hasFixedAmountQuote: group.hasFixedAmountQuote,
                
                // 使用处理后的保底数据
                minimumAmount: minimumAmount,
                minimumUnitPrice: minimumUnitPrice,
                minimumQuantity: minimumQuantity,
                expandMultiple: group.expandMultiple, // 扩大倍数字段
                
                // 其他字段
                fixedUsage: group.fixedUsage,
                fixedAmount: group.fixedAmount,
                maximumAmount: group.maximumAmount,
                discountCoefficient: group.discountCoefficient,
                fixedRebate: group.fixedRebate,
                cashReduce: group.cashReduce,
                credit: group.credit,
                discountType: group.discountType,
                fixedPrice: group.fixedPrice,
                fixedQuantity: group.fixedQuantity,
                ladderType:group.ladderType,
                // 添加ladderDiscountId，确保产品组关联正确
                ladderDiscountId: group.ladderDiscountId,
                quoteTypeValue:group.quoteTypeValue,
                isGift: group.isGift, // 是否赠送字段
                minimumGuaranteeType: group.minimumGuaranteeType // 保底类型字段
                
            };
        });
    }
    
    // 验证数据
    validateData() {
        // 至少有一个产品组或单产品
        if (this.productGroups.length === 0 && this.singleProducts.length === 0) {
            this.showError('请至少添加一个产品组或单产品');
            return false;
        }
        
        // 验证单产品
        for (let i = 0; i < this.singleProducts.length; i++) {
            const product = this.singleProducts[i];

            // 验证必填字段
            if (!product.productId) {
                this.showError(`单产品 #${i+1} 缺少产品ID`);
                return false;
            }

            // 验证利润率（税率）
            if (product.Region === null || product.Region === undefined || product.Region === '') {
                this.showError(`单产品 #${i+1} 区域必填`);
                return false;
            }

            // 验证报价方式
            // if (product.hasFixedAmountQuote) {
            //     // 固定金额报价方式验证 - 已注释掉
            //     if (product.fixedUsage === null || product.fixedUsage === undefined || product.fixedUsage === '') {
            //         this.showError(`单产品 #${i+1} 的固定金额报价方式缺少固定数量`);
            //         return false;
            //     }
            //     if (product.fixedAmount === null || product.fixedAmount === undefined || product.fixedAmount === '') {
            //         this.showError(`单产品 #${i+1} 的固定金额报价方式缺少固定单价`);
            //         return false;
            //     }
            // } else
            if (product.hasProductDiscountQuote) {
                // 产品折扣报价方式验证（MSP，MaaS）
                if (product.discountCoefficient === null || product.discountCoefficient === undefined || product.discountCoefficient === '') {
                    this.showError(`单产品 #${i+1} 的产品折扣报价方式缺少折扣系数`);
                    return false;
                }
                // 折扣系数不可为0（仅MSP和MaaS产品线）
                if ((this.isMspProduct || this.isMassProduct) && parseFloat(product.discountCoefficient) <= 0) {
                    this.showError(`单产品 #${i+1} 的产品折扣报价方式的折扣系数不能为0`);
                    return false;
                }
            } else if (product.hasMinimumUnitPriceQuote && !product.hasMinUnitPriceSharedLadderUsagePriceDown) {
                // 单价*数量报价方式验证（所有产品线）
                // 但是如果是组合报价方式（单价*数量+阶梯用量单价），则跳过此验证，使用专门的组合报价验证
                if (product.minimumUnitPrice === null || product.minimumUnitPrice === undefined || product.minimumUnitPrice === '') {
                    this.showError(`单产品 #${i+1} 的单价*数量报价方式缺少单价`);
                    return false;
                }
                // 单价不可为0，除非是赠送
                if (parseFloat(product.minimumUnitPrice) <= 0 && product.isGift !== '1') {
                    this.showError(`单产品 #${i+1} 的单价*数量报价方式的单价不能为0（除非选择赠送）`);
                    return false;
                }
                if (product.minimumQuantity === null || product.minimumQuantity === undefined || product.minimumQuantity === '') {
                    this.showError(`单产品 #${i+1} 的单价*数量报价方式缺少数量`);
                    return false;
                }
                // 数量可以为0，不需要额外校验

                if (product.expandMultiple && (isNaN(product.expandMultiple) || product.expandMultiple < 1 || product.expandMultiple > 99999)) {
                    this.showError(`单产品 #${i+1} 的扩大倍数必须是1-99999之间的整数`);
                    return false;
                }
            } else if (product.hasSharedLadderAmountDiscountZone ||
                      product.hasSharedLadderAmountDiscountDown ||
                      product.hasSharedLadderUsagePriceDown ||
                      product.hasMinAmountSharedLadderAmountDiscountDown ||
                      product.hasMinUnitPriceSharedLadderUsagePriceDown) {
                // 阶梯报价方式验证
                if (!Array.isArray(product.tiers) || product.tiers.length === 0) {
                    this.showError(`单产品 #${i+1} 的阶梯报价方式至少需要一个阶梯`);
                    return false;
                }

                
                if (product.hasSharedLadderUsagePriceDown) {
                    
                    if (product.expandMultiple && (isNaN(product.expandMultiple) || product.expandMultiple < 1 || product.expandMultiple > 99999)) {
                        this.showError(`单产品 #${i+1} 的扩大倍数必须是1-99999之间的整数`);
                        return false;
                    }
                }

                // 验证每个阶梯
                for (let k = 0; k < product.tiers.length; k++) {
                    const tier = product.tiers[k];

                    if (tier.lowerBound === '' || tier.lowerBound === null || tier.lowerBound === undefined) {
                        this.showError(`单产品 #${i+1} 的阶梯${k+1}未设置下限`);
                        return false;
                    }

                    // 允许最后一条阶梯的上限为空，其他阶梯必须设置上限
                    if (tier.upperBound === '' || tier.upperBound === null || tier.upperBound === undefined) {
                        if (k < product.tiers.length - 1) { // 不是最后一条阶梯
                            this.showError(`单产品 #${i+1} 的阶梯${k+1}未设置上限`);
                            return false;
                        }
                    }

                    if (!tier.unit) {
                        this.showError(`单产品 #${i+1} 的阶梯${k+1}未设置单位`);
                        return false;
                    }

                    if (tier.discount === '' || tier.discount === null || tier.discount === undefined) {
                        this.showError(`单产品 #${i+1} 的阶梯${k+1}未设置折扣`);
                        return false;
                    }

                    // 所有阶梯报价方式的折扣/单价都不可为0
                    if (parseFloat(tier.discount) <= 0) {
                        this.showError(`单产品 #${i+1} 的阶梯${k+1}的折扣/单价不能为0`);
                        return false;
                    }

                    if (!tier.calculationMethod) {
                        this.showError(`单产品 #${i+1} 的阶梯${k+1}未设置计算方式`);
                        return false;
                    }

                    // 上限必须大于下限（如果上限不为空）
                    if (tier.upperBound !== '' && tier.upperBound !== null && tier.upperBound !== undefined) {
                        if (parseFloat(tier.upperBound) <= parseFloat(tier.lowerBound)) {
                            this.showError(`单产品 #${i+1} 的阶梯${k+1}的上限必须大于下限`);
                            return false;
                        }
                    }
                }

                // 验证保底金额+共享阶梯金额折扣落区中的保底金额部分
                if (product.hasMinAmountSharedLadderAmountDiscountDown) {
                    if (product.minimumAmount === '' || product.minimumAmount === null || product.minimumAmount === undefined) {
                        this.showError(`单产品 #${i+1} 未设置保底金额`);
                        return false;
                    }
                    // 保底金额不可为0
                    if (parseFloat(product.minimumAmount) <= 0) {
                        this.showError(`单产品 #${i+1} 的保底金额不能为0`);
                        return false;
                    }
                    // 验证阶梯中的折扣不可为0
                    for (let k = 0; k < product.tiers.length; k++) {
                        const tier = product.tiers[k];
                        if (parseFloat(tier.discount) <= 0) {
                            this.showError(`单产品 #${i+1} 的保底金额+共享阶梯金额折扣中阶梯${k+1}的折扣不能为0`);
                            return false;
                        }
                    }
                }

                // 验证单价*数量+阶梯用量单价中的保底部分（算力，AI基础设施）
                if (product.hasMinUnitPriceSharedLadderUsagePriceDown) {
                    if (product.minimumUnitPrice === '' || product.minimumUnitPrice === null || product.minimumUnitPrice === undefined) {
                        this.showError(`单产品 #${i+1} 未设置单价`);
                        return false;
                    }
                    // 单价不可为0，除非是赠送
                    if (parseFloat(product.minimumUnitPrice) <= 0 && product.isGift !== '1') {
                        this.showError(`单产品 #${i+1} 的单价*数量+阶梯用量单价的单价不能为0（除非选择赠送）`);
                        return false;
                    }
                    if (product.minimumQuantity === '' || product.minimumQuantity === null || product.minimumQuantity === undefined) {
                        this.showError(`单产品 #${i+1} 未设置保数量`);
                        return false;
                    }
                    // 数量不可为0（算力，AI基础设施产品线）
                    if ((this.isComputingProduct || this.isAIInfrastructureProduct) && parseFloat(product.minimumQuantity) <= 0) {
                        this.showError(`单产品 #${i+1} 的单价*数量+阶梯用量单价的数量不能为0`);
                        return false;
                    }

                    // 验证阶梯中的单价不可为0
                    if (product.tiers && product.tiers.length > 0) {
                        for (let k = 0; k < product.tiers.length; k++) {
                            const tier = product.tiers[k];
                            if (parseFloat(tier.discount) <= 0) {
                                this.showError(`单产品 #${i+1} 的单价*数量+阶梯用量单价中阶梯${k+1}的单价不能为0`);
                                return false;
                            }
                        }
                        // 第一个阶梯的上限会自动设置为保底数量，不需要校验
                    }

                    if (product.expandMultiple && (isNaN(product.expandMultiple) || product.expandMultiple < 1 || product.expandMultiple > 99999)) {
                        this.showError(`单产品 #${i+1} 的扩大倍数必须是1-99999之间的整数`);
                        return false;
                    }
                }
            } else {
                // 如果没有任何报价方式，提示用户
                this.showError(`单产品 #${i+1} 至少需要一种报价方式`);
                return false;
            }
        }
        
        // 验证每个产品组
        for (let i = 0; i < this.productGroups.length; i++) {
            const group = this.productGroups[i];
            
            // 每个产品组至少有一个产品
            if (group.products.length === 0) {
                this.showError(`组合${group.groupNumber}中至少需要一个产品`);
                return false;
            }
            
            // 每个产品组至少有一种报价方式
            if (!group.hasAnyQuoteType) {
                this.showError(`组合${group.groupNumber}中至少需要一种报价方式`);
                return false;
            }
            
            // 验证产品数据
            if (!this.validateProductFields(group)) {
                    return false;
                }
                
            // 检查报价方式类型
            const isSharedLadderAmountDiscountDown = group.hasSharedLadderAmountDiscountDown || 
                                                   group.hasMinAmountSharedLadderAmountDiscountDown;
            const isSharedLadderUsagePriceDown = group.hasSharedLadderUsagePriceDown ||
                                               group.hasMinUnitPriceSharedLadderUsagePriceDown;
            const isSharedLadderAmountDiscountZone = group.hasSharedLadderAmountDiscountZone;
                
            // 验证阶梯报价 - 只验证仍然支持的共享阶梯类型
            if (isSharedLadderAmountDiscountZone || isSharedLadderAmountDiscountDown || isSharedLadderUsagePriceDown) {
                // 至少有一个阶梯
                if (group.tiers.length === 0) {
                    this.showError(`组合${group.groupNumber}中的阶梯报价至少需要一个阶梯`);
                    return false;
                }

               
                if (group.hasSharedLadderUsagePriceDown && !group.hasMinUnitPriceSharedLadderUsagePriceDown) {
                   
                    if (group.expandMultiple && (isNaN(group.expandMultiple) || group.expandMultiple < 1 || group.expandMultiple > 99999)) {
                        this.showError(`组合${group.groupNumber}中的扩大倍数必须是1-99999之间的整数`);
                        return false;
                    }
                }
                
                for (let k = 0; k < group.tiers.length; k++) {
                    const tier = group.tiers[k];

                    // 必填字段验证
                    if (tier.lowerBound === '' || tier.lowerBound === null || tier.lowerBound === undefined) {
                        this.showError(`组合${group.groupNumber}中的阶梯${k+1}未设置下限`);
                        return false;
                    }

                    // 允许最后一条阶梯的上限为空，其他阶梯必须设置上限
                    if (tier.upperBound === '' || tier.upperBound === null || tier.upperBound === undefined) {
                        if (k < group.tiers.length - 1) { // 不是最后一条阶梯
                            this.showError(`组合${group.groupNumber}中的阶梯${k+1}未设置上限`);
                            return false;
                        }
                    }

                    if (!tier.unit) {
                        this.showError(`组合${group.groupNumber}中的阶梯${k+1}未设置单位`);
                        return false;
                    }

                    if (tier.discount === '' || tier.discount === null || tier.discount === undefined) {
                        this.showError(`组合${group.groupNumber}中的阶梯${k+1}未设置折扣`);
                        return false;
                    }

                    // 所有阶梯报价方式的折扣/单价都不可为0
                    if (parseFloat(tier.discount) <= 0) {
                        this.showError(`组合${group.groupNumber}中的阶梯${k+1}的折扣/单价不能为0`);
                        return false;
                    }

                    if (!tier.calculationMethod) {
                        this.showError(`组合${group.groupNumber}中的阶梯${k+1}未设置计算方式`);
                        return false;
                    }

                    // 上限必须大于下限（如果上限不为空）
                    if (tier.upperBound !== '' && tier.upperBound !== null && tier.upperBound !== undefined) {
                        if (parseFloat(tier.upperBound) <= parseFloat(tier.lowerBound)) {
                            this.showError(`组合${group.groupNumber}中的阶梯${k+1}的上限必须大于下限`);
                            return false;
                        }
                    }
                }
            }
            
            // 验证保底金额+共享阶梯金额折扣落区中的保底金额部分
            if (group.hasMinAmountSharedLadderAmountDiscountDown) {
                if (group.minimumAmount === '' || group.minimumAmount === null || group.minimumAmount === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置保底金额`);
                    return false;
                }
                // 保底金额不可为0
                if (parseFloat(group.minimumAmount) <= 0) {
                    this.showError(`组合${group.groupNumber}中的保底金额不能为0`);
                    return false;
                }

                // 验证是否有阶梯，且阶梯单位为金额，计算方式为落区
                if (group.tiers.length === 0) {
                    this.showError(`组合${group.groupNumber}中的保底金额+共享阶梯金额折扣落区报价方式需要至少一个阶梯`);
                    return false;
                }

                // 验证阶梯中的折扣不可为0
                for (let k = 0; k < group.tiers.length; k++) {
                    const tier = group.tiers[k];
                    if (parseFloat(tier.discount) <= 0) {
                        this.showError(`组合${group.groupNumber}中的保底金额+共享阶梯金额折扣中阶梯${k+1}的折扣不能为0`);
                        return false;
                    }
                }

                // const firstTier = group.tiers[0];
                // if (firstTier.unit !== '金额' || firstTier.calculationMethod !== '落区') {
                //     this.showError(`组合${group.groupNumber}中的保底金额+共享阶梯金额折扣落区报价方式的阶梯单位必须为"金额"，计算方式必须为"落区"`);
                //     return false;
                // }
            }
            
            // 验证单价*数量+共享阶梯用量单价落区中的保底部分（算力，AI基础设施）
            if (group.hasMinUnitPriceSharedLadderUsagePriceDown) {
                if (group.minimumUnitPrice === '' || group.minimumUnitPrice === null || group.minimumUnitPrice === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置单价`);
                    return false;
                }
                // 单价不可为0，除非是赠送
                if (parseFloat(group.minimumUnitPrice) <= 0 && group.isGift !== '1') {
                    this.showError(`组合${group.groupNumber}中的单价*数量+阶梯用量单价的单价不能为0（除非选择赠送）`);
                    return false;
                }
                if (group.minimumQuantity === '' || group.minimumQuantity === null || group.minimumQuantity === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置数量`);
                    return false;
                }
                // 数量不可为0（算力，AI基础设施产品线）
                if ((this.isComputingProduct || this.isAIInfrastructureProduct) && parseFloat(group.minimumQuantity) <= 0) {
                    this.showError(`组合${group.groupNumber}中的单价*数量+阶梯用量单价的数量不能为0`);
                    return false;
                }

                if (group.expandMultiple && (isNaN(group.expandMultiple) || group.expandMultiple < 1 || group.expandMultiple > 99999)) {
                    this.showError(`组合${group.groupNumber}中的扩大倍数必须是1-99999之间的整数`);
                    return false;
                }

                // 验证是否有阶梯，且阶梯单位为用量，计算方式为落区
                if (group.tiers.length === 0) {
                    this.showError(`组合${group.groupNumber}中的单价*数量+阶梯用量单价报价方式需要至少一个阶梯`);
                    return false;
                }

                // 验证阶梯中的单价不可为0
                if (group.tiers && group.tiers.length > 0) {
                    for (let k = 0; k < group.tiers.length; k++) {
                        const tier = group.tiers[k];
                        if (parseFloat(tier.discount) <= 0) {
                            this.showError(`组合${group.groupNumber}中的单价*数量+阶梯用量单价中阶梯${k+1}的单价不能为0`);
                            return false;
                        }
                    }
                    // 第一个阶梯的上限会自动设置为保底数量，不需要校验
                }

                // const firstTier = group.tiers[0];
                // if (firstTier.unit !== '用量' || firstTier.calculationMethod !== '落区') {
                //     this.showError(`组合${group.groupNumber}中的单价*数量+共享阶梯用量单价落区报价方式的阶梯单位必须为"用量"，计算方式必须为"落区"`);
                //     return false;
                // }
            }
            
            // 验证固定金额
            if (group.hasFixedAmountQuote) {
                if (group.fixedAmount === '' || group.fixedAmount === null || group.fixedAmount === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置固定金额`);
                    return false;
                }
                if (group.fixedUsage === '' || group.fixedUsage === null || group.fixedUsage === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置固定数量`);
                    return false;
                }
            }
            
            // 验证产品折扣（MSP，MaaS）
            if (group.hasProductDiscountQuote) {
                if (group.discountCoefficient === '' || group.discountCoefficient === null || group.discountCoefficient === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置折扣系数`);
                    return false;
                }
                // 折扣系数不可为0（仅MSP和MaaS产品线）
                if ((this.isMspProduct || this.isMassProduct) && parseFloat(group.discountCoefficient) <= 0) {
                    this.showError(`组合${group.groupNumber}中的产品折扣报价方式的折扣系数不能为0`);
                    return false;
                }
                // if (group.fixedRebate === '' || group.fixedRebate === null || group.fixedRebate === undefined) {
                //     this.showError(`组合${group.groupNumber}中未设置固定返利`);
                //     return false;
                // }
                // if (group.cashReduce === '' || group.cashReduce === null || group.cashReduce === undefined) {
                //     this.showError(`组合${group.groupNumber}中未设置现金减免`);
                //     return false;
                // }
                // if (group.credit === '' || group.credit === null || group.credit === undefined) {
                //     this.showError(`组合${group.groupNumber}中未设置Credit`);
                //     return false;
                // }
            }

            // 验证单价*数量报价方式（所有产品线）
            // 但是如果是组合报价方式（单价*数量+阶梯用量单价），则跳过此验证，使用专门的组合报价验证
            if (group.hasMinimumUnitPriceQuote && !group.hasMinUnitPriceSharedLadderUsagePriceDown) {
                if (group.minimumUnitPrice === '' || group.minimumUnitPrice === null || group.minimumUnitPrice === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置单价`);
                    return false;
                }
                // 单价不可为0，除非是赠送
                if (parseFloat(group.minimumUnitPrice) <= 0 && group.isGift !== '1') {
                    this.showError(`组合${group.groupNumber}中的单价*数量报价方式的单价不能为0（除非选择赠送）`);
                    return false;
                }
                if (group.minimumQuantity === '' || group.minimumQuantity === null || group.minimumQuantity === undefined) {
                    this.showError(`组合${group.groupNumber}中未设置数量`);
                    return false;
                }
                // 数量可以为0，不需要额外校验

                if (group.expandMultiple && (isNaN(group.expandMultiple) || group.expandMultiple < 1 || group.expandMultiple > 99999)) {
                    this.showError(`组合${group.groupNumber}中的扩大倍数必须是1-99999之间的整数`);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    // 验证产品数据
    validateProductFields(group) {
        // 每个产品组至少有一个产品
        if (group.products.length === 0) {
            this.showError(`组合${group.groupNumber}中至少需要一个产品`);
            return false;
        }
        
        // 检查产品组内是否有重复产品
        const productIds = new Set();
        for (let j = 0; j < group.products.length; j++) {
            const product = group.products[j];
            
            // 检查产品重复
            // if (product.productId && productIds.has(product.productId)) {
            //     this.showError(`组合${group.groupNumber}中存在重复产品，每个产品在同一组内只能添加一次`);
            //     return false;
            // }
            
            if (product.productId) {
                productIds.add(product.productId);
            }
            
            // 必填字段验证
            if (!product.productId) {
                this.showError(`组合${group.groupNumber}中的产品${j+1}未选择产品`);
                return false;
            }
            
            if (!product.Region) {
                this.showError(`组合${group.groupNumber}中的产品${j+1}未设置区域`);
                return false;
            }
            
            // 移除其他字段的必填校验
            // if (!product.unitPrice) {
            //     this.showError(`组合${group.groupNumber}中的产品${j+1}未设置排价`);
            //     return false;
            // }
            
            // if (!product.customerAccountId) {
            //     this.showError(`组合${group.groupNumber}中的产品${j+1}未设置客户账号ID`);
            //     return false;
            // }
        }
        
        return true;
    }
    
    // 显示错误提示
    showError(message) {
        this.dispatchEvent(
            new ShowToastEvent({
                title: '验证错误',
                message: message,
                variant: 'error'
            })
        );
    }
    
    // 重置数据
    handleReset() {
        console.log('重置数据');
        // this.initializeEmptyGroups();
        
        this.dispatchEvent(
            new ShowToastEvent({
                title: '成功',
                message: '数据已重置',
                variant: 'success'
            })
        );
    }
    
    // 错误处理工具方法
    reduceErrors(error) {
        if (!error) {
            return '未知错误';
        }
        
        // 如果是字符串，直接返回
        if (typeof error === 'string') {
            return error;
        }
        
        // 如果有body.message，返回它
        if (error.body && error.body.message) {
            return error.body.message;
        }
        
        // 如果有message属性，返回它
        if (error.message) {
            return error.message;
        }
        
        // 返回字符串化的错误
        return JSON.stringify(error);
    }

    // 查看阶梯详情
    handleViewLadderDetails(event) {
        const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
        console.log('查看阶梯详情，产品组索引:', groupIndex);
        
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            const group = this.productGroups[groupIndex];
            
            // 创建阶梯详情内容
            let message = `产品组${group.groupNumber}的阶梯报价详情：\n\n`;
            
            if (group.tiers && group.tiers.length > 0) {
                group.tiers.forEach((tier, index) => {
                    message += `阶梯${index + 1}：\n`;
                    message += `下限：${tier.lowerBound}\n`;
                    message += `上限：${tier.upperBound}\n`;
                    message += `单位：${tier.unit}\n`;
                    message += `折扣：${tier.discount}\n`;
                    message += `计算方式：${tier.calculationMethod}\n\n`;
                });
            } else {
                message += '暂无阶梯数据';
            }
            
            // 显示详情
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '阶梯报价详情',
                    message: message,
                    variant: 'info',
                    mode: 'sticky'
                })
            );
        }
    }

    // 在新窗口中打开产品组编辑页面
    @api
    openInNewWindow(recordId) {
        // 构建URL参数
        const url = `/lightning/cmp/c__quoteLineGroupCreate?c__recordId=${recordId}&c__startInEditMode=true`;
        // 在新窗口中打开
        window.open(url, '_blank');
    }

    // // 处理表格行操作
    // handleRowAction(event) {
    //     const action = event.detail.action;
    //     const row = event.detail.row;
    //     const groupIndex = event.target.closest('[data-group-index]').dataset.groupIndex;
        
    //     if (action.name === 'delete') {
    //         // 找到产品在数组中的索引
    //         const productIndex = this.productGroups[groupIndex].products.findIndex(product => product.id === row.id);
    //         if (productIndex !== -1) {
    //             this.handleDeleteProduct({
    //                 target: {
    //                     dataset: {
    //                         groupIndex: groupIndex,
    //                         productIndex: productIndex
    //                     }
    //                 }
    //             });
    //         }
    //     }
    // }

    // 处理行选择事件
    handleRowSelection(event) {
        const selectedRows = event.detail.selectedRows;
        const groupIndex = parseInt(event.target.closest('[data-group-index]').dataset.groupIndex, 10);
        
        console.log('Selected Rows:', JSON.stringify(selectedRows));
        console.log('Group Index:', groupIndex);
        
        // 确保selectedRows是一个对象
        if (!this.selectedRows) {
            this.selectedRows = {};
        }
        
        // 更新当前组的选中行
        this.selectedRows[groupIndex] = selectedRows;
        
        console.log(`产品组 ${groupIndex} 选中了 ${selectedRows.length} 行产品`);
        console.log('All selectedRows:', JSON.stringify(this.selectedRows));
    }

    // 处理批量删除按钮点击
    handleDeleteLine(event) {
        const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
        console.log('批量删除产品行，产品组索引:', groupIndex);
        console.log('当前所有选中行:', JSON.stringify(this.selectedRows));
        
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            const selectedRowsForGroup = this.selectedRows[groupIndex] || [];
            
            console.log(`产品组 ${groupIndex} 选中行数:`, selectedRowsForGroup.length);
            
            if (selectedRowsForGroup.length === 0) {
                // 如果没有选中行，显示提示消息
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: '提示',
                        message: '请先选择要删除的产品行',
                        variant: 'info'
                    })
                );
                return;
            }
            
            // 获取选中行的ID
            const selectedIds = selectedRowsForGroup.map(row => row.id);
            console.log('要删除的产品IDs:', selectedIds);
            
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 过滤掉选中的行
            updatedGroups[groupIndex].products = updatedGroups[groupIndex].products.filter(
                product => !selectedIds.includes(product.id)
            );
            
            this.productGroups = updatedGroups;
            
            // 清空该组的选中行
            this.selectedRows[groupIndex] = [];
            
            // 强制刷新表格
            this.template.querySelector(`[data-group-index="${groupIndex}"] lightning-datatable`).selectedRows = [];
            
            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: `已删除 ${selectedIds.length} 个产品行`,
                    variant: 'success'
                })
            );
        }
    }
    
    // 处理单元格值更改
    async handleCellChange(event) {
        const draftValues = event.detail.draftValues;
        if (!draftValues || draftValues.length === 0) {
            return;
        }
        
        console.log('单元格编辑值:', JSON.stringify(draftValues));
        
        // 创建新的数组以确保响应式更新
        const updatedGroups = [...this.productGroups];
        const groupIndex = parseInt(event.target.closest('[data-group-index]').dataset.groupIndex, 10);
        const datatable = event.target;
        
        // 验证所有必填字段是否已填写
        let hasError = false;
        
        // 处理每个编辑过的单元格
        for (let i = 0; i < draftValues.length; i++) {
            const draftValue = draftValues[i];
            const productId = draftValue.id;
            const productObj =  updatedGroups[groupIndex].products.find(product => product.id === productId);

            //牌价查询（仅MaaS和AI Search产品线）
            console.log('productObj.productId***',productObj.productId);
            console.log('productId***',productId);
            console.log('this.recordId**',this.recordId);
            console.log('shouldQueryListPrice***', this.shouldQueryListPrice);

            if ('Region' in draftValue && (draftValue.Region != null && draftValue.Region != undefined && draftValue.Region != '')) {
                // 只有MaaS和AI Search产品线才查询牌价
                if (this.shouldQueryListPrice) {
                    await getProductAndAreaListPrice({
                        productId: productObj.productId,
                        areaStr:draftValue.Region,
                        quoteId:this.recordId
                    }).then(result => {
                        console.log('产品组 getProductAndAreaListPrice res:',JSON.stringify(result));
                        if(result.isSuccess !='true'){
                            // 显示错误提示
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    title: '错误',
                                    message: result.msg,
                                    variant: 'error'
                                })
                            );
                            draftValue.Region='';
                            hasError = true;
                            // break;
                        }else{
                            draftValue.listPrice=result.amount;
                            draftValue.listPriceCurrency=result.currencyCode;
                            draftValue.pricingId = result.pricing;
                        }


                    }).catch(error => {
                        console.error('查询牌价:', this.reduceErrors(error));
                        this.showError('查询牌价: ' + this.reduceErrors(error));
                        // this.initializeEmptyGroups();
                    })
                } else {
                    console.log('当前产品线不需要查询牌价，仅更新区域信息');
                }
            }
            
            // 验证利润率字段是否已填写
            if ('taxRate' in draftValue && (draftValue.taxRate === null || draftValue.taxRate === undefined || draftValue.taxRate === '')) {
                // 显示错误提示
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: '错误',
                        message: '利润率是必填字段，请输入有效的利润率值',
                        variant: 'error'
                    })
                );
                hasError = true;
                break;
            }
            
            // 查找产品在当前组中的索引
            const products = updatedGroups[groupIndex].products;
            let productFound = false;
            
            for (let productIndex = 0; productIndex < products.length; productIndex++) {
                if (products[productIndex].id === productId) {
                    productFound = true;
                    // 获取所有更改的字段
                    Object.keys(draftValue).forEach(field => {
                        if (field !== 'id') { // 允许编辑基准价字段
                            // 更新字段值
                            products[productIndex][field] = draftValue[field];
                            console.log(`更新产品字段，组: ${groupIndex}, 产品: ${productIndex}, 字段: ${field}, 值: ${draftValue[field]}`);
                        }
                    });
                    break;
                }
            }
            
            // 如果在当前组中没有找到，则检查其他组
            if (!productFound) {
                for (let otherGroupIndex = 0; otherGroupIndex < updatedGroups.length; otherGroupIndex++) {
                    if (otherGroupIndex === groupIndex) continue; // 跳过当前组
                    
                    const otherProducts = updatedGroups[otherGroupIndex].products;
                    for (let productIndex = 0; productIndex < otherProducts.length; productIndex++) {
                        if (otherProducts[productIndex].id === productId) {
                            // 获取所有更改的字段
                            Object.keys(draftValue).forEach(field => {
                                if (field !== 'id') { // 允许编辑基准价字段
                                    // 更新字段值
                                    otherProducts[productIndex][field] = draftValue[field];
                                    console.log(`更新产品字段，组: ${otherGroupIndex}, 产品: ${productIndex}, 字段: ${field}, 值: ${draftValue[field]}`);
                                }
                            });
                            break;
                        }
                    }
                }
            }
        }
        
        if (hasError) {
            // 如果有错误，不更新数据
            return;
        }else{
            
            // 更新数据
            this.productGroups = updatedGroups;
            
            // 清除草稿值，确保表格恢复到查看模式
            if (datatable) {
                datatable.draftValues = [];
            }
            
            // 显示成功消息
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '产品数据已更新',
                    variant: 'success'
                })
            );

        }
        
    }

    // 获取指定组的选中行
    getSelectedRowsFor(groupIndex) {
        return this.selectedRows[groupIndex] || [];
    }

    // 检查产品选择结果
    // checkProductSelection() {
    //     // 这个方法现在不再使用localStorage，而是通过事件接收产品数据
    //     // 在addProductMent事件处理程序中处理产品添加
    //     console.log('checkProductSelection - 等待子组件事件传递产品数据');
    // }
    
    // 处理从productInfo子组件接收的产品数据
    addProductMent(event) {
        // 根据是否为MaaS产品决定是否显示复制按钮
        const actions= this.isEditMode ? [
                { label: '报价方式', name: 'quote_type' },
                ...(this.isMassProduct ? [] : [{ label: '复制', name: 'clone' }]),
                ...(this.isMspProduct ? [{ label: '配置客户账号id', name: 'configure_customer_ids' }]:[] ),
            ] : [
                { label: '报价方式', name: 'quote_type' },
                // { label: '配置客户账号id', name: 'configure_customer_ids' }
            ]
        const productList = event.detail.productList;
        const groupIndex = event.detail.groupIndex;
        console.log('接收到添加产品事件，产品列表:', JSON.stringify(productList), '产品组索引:', groupIndex);
        //判断是否是添加单产品
        if (groupIndex === 'single') {
            console.log('添加单产品');
            if (productList && productList.length > 0) {
                // 准备产品数据，确保包含所有必要的字段
                const productsData = productList.map(product => {
                    return {
                        productId: product.productId,
                        unitPrice: product.unitPrice || 1,
                        listPrice: product.listPrice || 0,
                        taxRate: product.taxRate || 0,
                        customerAccountId: product.customerAccountId || '',
                        profitDescription: product.profitDescription || '',
                        Description: product.Description || '',
                        QuoteLineStartDate: product.QuoteLineStartDate || null,
                        QuoteLineEndDate: product.QuoteLineEndDate || null,
                        Region: product.Region || '',
                        OneTimeFee: product.OneTimeFee || '否',
                        chargeType: product.chargeType || '计费',
                        isGroup: false // 确保标记为单产品
                        
                    };
                });
                
                // 显示加载状态
                this.isLoading = true;
                
                // 调用后端方法临时处理单产品数据
                handleProductInfoProductsTemp({
                    productsJSON: JSON.stringify(productsData),
                    quoteId: this.recordId,
                    productGroupId: 'single' // 使用'single'标识符表示这是单产品
                })
                .then(result => {
                    console.log('临时添加单产品成功:', result);

                    if (result.success) {
                        // 将产品添加到临时列表和当前单产品显示
                        const processedProducts = result.products || [];

                        const tempprocessedProducts = processedProducts.map((product) => {
                            // 计算汇率差系数
                            let unitPrice = product.unitPrice;
                            // 对于MSP产品线且存在汇率差的情况，强制设置为1.02
                            if (this.isMspProduct && this.quoteInfo?.exchangeRateDifferenceValue === '1') {
                                unitPrice = 1.02;
                            } else if (!unitPrice || unitPrice === '' || unitPrice === null || unitPrice === undefined) {
                                // 其他情况下，如果unitPrice为空，则设置为1.0
                                unitPrice = 1.0;
                            }

                            return {
                                ...product,
                                unitPrice: unitPrice,
                                exchangeRateDifferenceLabel: this.exchangeRateDifference,
                                exchangeRateDifferenceValue: this.quoteInfo?.exchangeRateDifferenceValue || '2',
                                dynamicActions: actions
                            };
                        });

                        // 添加到当前单产品的显示列表
                        this.singleProducts = [...this.singleProducts, ...tempprocessedProducts];

                        // 更新单产品的三级产品标签
                        this.updateSingleProductTags();
                        

                        // 将产品添加到临时列表，用于后续保存
                        const tempProductData = {
                            products: processedProducts,
                            isGroup: false
                        };
                        this.tempSingleProducts = [...this.tempSingleProducts, tempProductData];

                        // 显示成功提示
                        this.dispatchEvent(
                            new ShowToastEvent({
                                title: '成功',
                                message: `已添加 ${processedProducts.length} 个单产品，请点击保存按钮统一保存`,
                                variant: 'success'
                            })
                        );

                        // 关闭模态框
                        this.isModalOpen = false;
                    } else {
                        this.showError('添加单产品失败: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('临时添加单产品失败:', this.reduceErrors(error));
                    this.showError('添加单产品失败: ' + this.reduceErrors(error));
                })
                .finally(() => {
                    this.isLoading = false;
                });
            } else {
                this.showError('未选择任何产品');
            }
            return;
        }
                
        // 判断是否有有效的产品组
        if (groupIndex >= 0 && groupIndex < this.productGroups.length && productList && productList.length > 0) {
            // 获取当前产品组ID（Ladder_discount__c值）- 可能为空
            const currentGroup = this.productGroups[groupIndex];
            const productGroupId = currentGroup.ladderDiscountId;
           
            // 无论productGroupId是否存在，都调用后端方法
            // 后端会在需要时自动创建产品组
            this.isLoading = true;
            
            // 准备产品数据，确保包含所有必要的字段3
            const productsData = productList.map(product => {
                return {
                    productId: product.productId,
                    unitPrice: product.unitPrice || 1,
                    listPrice: product.listPrice || 0,
                    taxRate: product.taxRate || 0,
                    customerAccountId: product.customerAccountId || '',
                    profitDescription: product.profitDescription || '',
                    Description: product.Description || '',
                    QuoteLineStartDate: product.QuoteLineStartDate || this.quoteInfo?.startDate || null,
                    QuoteLineEndDate: product.QuoteLineEndDate || this.quoteInfo?.endDate || null,
                    Region: product.Region || '',
                    OneTimeFee: product.OneTimeFee || '否',
                    chargeType: product.chargeType || '计费'
                };
            });
            
            handleProductInfoProductsTemp({
                productsJSON: JSON.stringify(productsData),
                quoteId: this.recordId,
                productGroupId: productGroupId
            })
            .then(result => {
                console.log('临时添加ProductInfo产品到产品组成功:', result);

                if (result.success) {
                    // 将产品添加到临时列表和当前产品组显示
                    const processedProducts = result.products || [];

                    const tempprocessedProducts = processedProducts.map((product) => {
                            // 计算汇率差系数
                            let unitPrice = product.unitPrice;
                            // 对于MSP产品线且存在汇率差的情况，强制设置为1.02
                            if (this.isMspProduct && this.quoteInfo?.exchangeRateDifferenceValue === '1') {
                                unitPrice = 1.02;
                            } else if (!unitPrice || unitPrice === '' || unitPrice === null || unitPrice === undefined) {
                                // 其他情况下，如果unitPrice为空，则设置为1.0
                                unitPrice = 1.0;
                            }

                            return {
                                ...product,
                                unitPrice: unitPrice,
                                exchangeRateDifferenceLabel: this.exchangeRateDifference,
                                exchangeRateDifferenceValue: this.quoteInfo?.exchangeRateDifferenceValue || '2'
                            };
                        });

                    // 添加到当前产品组的显示列表
                    if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
                        const updatedGroups = [...this.productGroups];
                        updatedGroups[groupIndex].products = [...updatedGroups[groupIndex].products, ...tempprocessedProducts];

                        const groupProducts = Array.isArray(updatedGroups[groupIndex].products) ? this.processProductData(updatedGroups[groupIndex].products) : [];
                        const maasLevelThreeProducts = groupProducts.filter(p => p.level1 === 'MaaS');
                        const levelThreeProductNames = [...new Set(maasLevelThreeProducts.map(p => p.level3).filter(Boolean))];

                        // 处理三级产品标签显示逻辑
                        const allLevelThreeProducts = levelThreeProductNames.map(name => ({ name, id: (updatedGroups[groupIndex].id || 'temp_group') + '_' + name }));
                        const displayedLevelThreeProducts = allLevelThreeProducts.slice(0, 4).map(product => ({ ...product, shouldDisplay: true }));
                        const hasMoreTags = allLevelThreeProducts.length > 4;
                        const remainingCount = Math.max(0, allLevelThreeProducts.length - 4);
                        const moreTagsLabel = `更多 (${remainingCount})`;

                        updatedGroups[groupIndex].levelThreeProducts = displayedLevelThreeProducts;
                        updatedGroups[groupIndex].allLevelThreeProducts = allLevelThreeProducts;
                        updatedGroups[groupIndex].hasMoreTags = hasMoreTags;
                        updatedGroups[groupIndex].moreTagsLabel = moreTagsLabel;

                        this.productGroups = updatedGroups;


                    }

                    // 将产品添加到临时列表，用于后续保存
                    const tempProductData = {
                        groupIndex: groupIndex,
                        productGroupId: productGroupId,
                        products: processedProducts,
                        isGroup: true
                    };
                    this.tempProducts = [...this.tempProducts, tempProductData];

                    // 显示成功提示
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: '成功',
                            message: `已添加 ${processedProducts.length} 个产品到产品组，请点击保存按钮统一保存`,
                            variant: 'success'
                        })
                    );

                    // 关闭模态框
                    this.isModalOpen = false;
                } else {
                    this.showError('添加产品失败: ' + result.message);
                }
            })
            .catch(error => {
                console.error('临时添加ProductInfo产品到产品组失败:', this.reduceErrors(error));
                this.showError('添加产品失败: ' + this.reduceErrors(error));
            })
            .finally(() => {
                this.isLoading = false;
            });
        } else {
            // 如果产品组索引无效或产品列表为空，显示错误提示
            this.showError(`无效的产品组索引或产品列表为空`);
        }
    }

    // 将产品添加到指定的产品组
    addProductsToGroup(products, groupIndex) {
        if (groupIndex >= 0 && groupIndex < this.productGroups.length && products && products.length > 0) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 将新产品添加到指定产品组
            updatedGroups[groupIndex].products = [...updatedGroups[groupIndex].products, ...products];
            
            this.productGroups = updatedGroups;
            console.log('产品已添加到产品组', groupIndex, '当前产品数量:', this.productGroups[groupIndex].products.length);
            
            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: `已添加 ${products.length} 个产品到产品组 ${groupIndex + 1}`,
                    variant: 'success'
                })
            );
        }
    }
    
    // 控制单产品表格复选框显示
    get hideCheckboxColumn() {
        return !this.isEditMode; // 在只读模式下隐藏复选框列
    }

    // 控制产品组表格复选框显示（产品组始终隐藏勾选框）
    get hideProductGroupCheckboxColumn() {
        return true; // 产品组始终隐藏勾选框
    }

    // 控制表格行号显示
    get showRowNumberColumn() {
        return true; // 显示行号列
    }

    // 获取选中的单产品ID数组（用于datatable的selected-rows属性）
    get selectedSingleProductIds() {
        if (!this.selectedSingleProducts || this.selectedSingleProducts.length === 0) {
            return [];
        }
        return this.selectedSingleProducts.map(product => product.id);
    }
    
    // 添加固定金额报价方式 - 已注释掉
    // handleAddFixedAmountQuoteType() {
    //     console.log('添加固定金额报价方式');
    //
    //     if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
    //         // 创建新的数组以确保响应式更新
    //         const updatedGroups = [...this.productGroups];
    //
    //         // 设置固定金额报价标志和初始值（不再设置固定用量标志）
    //         updatedGroups[this.currentGroupIndex].hasFixedAmountQuote = true;
    //         updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
    //         updatedGroups[this.currentGroupIndex].quoteTypeValue = '固定金额';
    //
    //         // 设置默认值
    //         if (!updatedGroups[this.currentGroupIndex].fixedAmount) {
    //             updatedGroups[this.currentGroupIndex].fixedAmount = '';
    //         }
    //         if (!updatedGroups[this.currentGroupIndex].fixedUsage) {
    //             updatedGroups[this.currentGroupIndex].fixedUsage = '';
    //         }
    //
    //         this.productGroups = updatedGroups;
    //
    //
    //         // 显示成功提示
    //         this.dispatchEvent(
    //             new ShowToastEvent({
    //                 title: '成功',
    //                 message: '固定金额报价方式已添加',
    //                 variant: 'success'
    //             })
    //         );
    //     }
    // }

    // 添加共享阶梯金额折扣分区
    handleAddSharedLadderAmountDiscountZoneType() {
        console.log('添加阶梯金额折扣，产品组索引:', this.currentGroupIndex);
        
        if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 设置共享阶梯金额折扣分区标志
            updatedGroups[this.currentGroupIndex].hasSharedLadderAmountDiscountZone = true;
            updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
            updatedGroups[this.currentGroupIndex].quoteTypeLabel = '阶梯金额折扣';
            updatedGroups[this.currentGroupIndex].quoteTypeValue = '4';

            // 设置默认折扣类型为"产品折扣"
            updatedGroups[this.currentGroupIndex].discountType = '1';
            updatedGroups[this.currentGroupIndex].discountTypeLabel = '产品折扣';
            
            // 无论是否有阶梯，都创建一个新的空阶梯并设置默认值
            // 这样可以确保始终使用正确的默认值
            const emptyTier = this.createEmptyTier();
            emptyTier.unit = '金额';
            emptyTier.calculationMethod = '分区';
            emptyTier.lowerBound = '0';
            emptyTier.upperBound = '100000';
            emptyTier.discount = '90';
            emptyTier.unitNotEditable = true; // 单位不可编辑
            emptyTier.calculationMethodNotEditable = true; // 计算方式不可编辑
            emptyTier.isDeletable = true; // 第一个阶梯可删除
            
            // 如果没有阶梯，直接添加；如果有阶梯，添加一个新的
            if (updatedGroups[this.currentGroupIndex].tiers.length === 0) {
                updatedGroups[this.currentGroupIndex].tiers = [emptyTier];
            } else {
                // 先将所有现有阶梯设为不可删除
                updatedGroups[this.currentGroupIndex].tiers.forEach(tier => {
                    tier.isDeletable = false;
                });
                
                // 添加新阶梯
                updatedGroups[this.currentGroupIndex].tiers.push(emptyTier);
            }
            
            this.productGroups = updatedGroups;
         
            
            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '阶梯金额折扣报价方式已添加',
                    variant: 'success'
                })
            );
        }
    }


    // 添加保底金额+共享阶梯金额折扣落区
    handleAddMinAmountSharedLadderAmountDiscountDownType() {
        console.log('添加保底金额+共享阶梯金额折扣落区，产品组索引:', this.currentGroupIndex);
        
        if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 设置保底金额+共享阶梯金额折扣落区标志
            updatedGroups[this.currentGroupIndex].hasMinAmountSharedLadderAmountDiscountDown = true;
            updatedGroups[this.currentGroupIndex].hasMinimumAmountQuote = true; // 使用现有保底金额UI
            updatedGroups[this.currentGroupIndex].hasSharedLadderAmountDiscountDown = true; // 使用现有阶梯报价UI
            updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
            updatedGroups[this.currentGroupIndex].quoteTypeLabel = '保底金额+共享阶梯金额折扣落区'; // 设置报价方式值
            updatedGroups[this.currentGroupIndex].quoteTypeValue = '5'; // 设置报价方式值
            
            // 设置默认保底金额
            if (!updatedGroups[this.currentGroupIndex].minimumAmount) {
                updatedGroups[this.currentGroupIndex].minimumAmount = '';
            }
            
            // 创建默认阶梯
            const emptyTier = this.createEmptyTier();
            emptyTier.unit = '金额';
            emptyTier.calculationMethod = '落区';
            // 第一个阶梯的下限使用保底金额的值
            emptyTier.lowerBound = updatedGroups[this.currentGroupIndex].minimumAmount || '0';
            emptyTier.upperBound = '100000';
            emptyTier.discount = '90'; // 默认折扣90%
            emptyTier.unitNotEditable = true; // 单位不可编辑
            emptyTier.calculationMethodNotEditable = true; // 计算方式不可编辑
            emptyTier.isDeletable = true; // 第一个阶梯可删除
            
            // 如果没有阶梯，直接添加；如果有阶梯，添加一个新的
            if (updatedGroups[this.currentGroupIndex].tiers.length === 0) {
                updatedGroups[this.currentGroupIndex].tiers = [emptyTier];
            } else {
                // 先将所有现有阶梯设为不可删除
                updatedGroups[this.currentGroupIndex].tiers.forEach(tier => {
                    tier.isDeletable = false;
                });
                
                // 添加新阶梯
                updatedGroups[this.currentGroupIndex].tiers.push(emptyTier);
            }
            
            this.productGroups = updatedGroups;
           
            
            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '保底金额+共享阶梯金额折扣落区报价方式已添加',
                    variant: 'success'
                })
            );
        }
    }

    // 添加单价*数量+共享阶梯用量单价落区
    handleAddMinUnitPriceSharedLadderUsagePriceDownType() {
        console.log('添加单价*数量+阶梯用量单价，产品组索引:', this.currentGroupIndex);
        
        if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 设置单价*数量+共享阶梯用量单价落区标志
            updatedGroups[this.currentGroupIndex].hasMinUnitPriceSharedLadderUsagePriceDown = true;
            updatedGroups[this.currentGroupIndex].hasMinimumUnitPriceQuote = true; // 使用现有保底单价*保底数量UI
            updatedGroups[this.currentGroupIndex].hasSharedLadderUsagePriceDown = true; // 使用现有阶梯报价UI
            updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
            updatedGroups[this.currentGroupIndex].quoteTypeLabel = '单价*数量+阶梯用量单价'; // 设置报价方式值
            updatedGroups[this.currentGroupIndex].quoteTypeValue = '6'; // 设置报价方式值

            
            // 设置默认值
            if (!updatedGroups[this.currentGroupIndex].minimumUnitPrice) {
                updatedGroups[this.currentGroupIndex].minimumUnitPrice = '';
            }
            if (!updatedGroups[this.currentGroupIndex].minimumQuantity) {
                updatedGroups[this.currentGroupIndex].minimumQuantity = '';
            }
            if (!updatedGroups[this.currentGroupIndex].expandMultiple) {
                updatedGroups[this.currentGroupIndex].expandMultiple = 1;
            }
            
            // 创建默认阶梯
            const emptyTier = this.createEmptyTier();
            emptyTier.unit = '用量';
            emptyTier.calculationMethod = '落区';
            emptyTier.lowerBound = '0';
            // 第一个阶梯的上限使用保底数量的值
            emptyTier.upperBound = updatedGroups[this.currentGroupIndex].minimumQuantity || '100000';
            emptyTier.discount = '90'; // 默认单价为0.9
            emptyTier.unitNotEditable = true; // 单位不可编辑
            emptyTier.calculationMethodNotEditable = true; // 计算方式不可编辑
            emptyTier.isDeletable = true; // 第一个阶梯可删除
            
            // 如果没有阶梯，直接添加；如果有阶梯，添加一个新的
            if (updatedGroups[this.currentGroupIndex].tiers.length === 0) {
                updatedGroups[this.currentGroupIndex].tiers = [emptyTier];
            } else {
                // 先将所有现有阶梯设为不可删除
                updatedGroups[this.currentGroupIndex].tiers.forEach(tier => {
                    tier.isDeletable = false;
                });
                
                // 添加新阶梯
                updatedGroups[this.currentGroupIndex].tiers.push(emptyTier);
            }
            
            this.productGroups = updatedGroups;
            
            
            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '单价*数量+阶梯用量单价报价方式已添加',
                    variant: 'success'
                })
            );
        }
    }
        // 添加产品折扣报价方式
    handleAddProductDiscountQuoteType() {
        console.log('添加产品折扣报价方式，产品组索引:', this.currentGroupIndex);
        
        if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 设置产品折扣报价标志和初始值
            updatedGroups[this.currentGroupIndex].hasProductDiscountQuote = true;
            updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
            console.log('是否展示产品折扣***********',  updatedGroups[this.currentGroupIndex].hasProductDiscountQuote);
            // 设置默认值
            if (!updatedGroups[this.currentGroupIndex].discountCoefficient) {
                updatedGroups[this.currentGroupIndex].discountCoefficient = '';
            }
            if (!updatedGroups[this.currentGroupIndex].fixedRebate) {
                updatedGroups[this.currentGroupIndex].fixedRebate = '';
            }
            if (!updatedGroups[this.currentGroupIndex].cashReduce) {
                updatedGroups[this.currentGroupIndex].cashReduce = '';
            }
            if (!updatedGroups[this.currentGroupIndex].credit) {
                updatedGroups[this.currentGroupIndex].credit = '';
            }
            // 设置默认折扣类型为"产品折扣" - 每次添加都重新设置
            updatedGroups[this.currentGroupIndex].discountType = '1';
            updatedGroups[this.currentGroupIndex].discountTypeLabel = '产品折扣';
            
            this.productGroups = updatedGroups;

        }
    }

    // 添加单价*数量报价方式
    handleAddMinimumUnitPriceQuoteType() {
        console.log('添加单价*数量报价方式，产品组索引:', this.currentGroupIndex);

        if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];

            // 设置单价*数量报价标志和初始值
            updatedGroups[this.currentGroupIndex].hasMinimumUnitPriceQuote = true;
            updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
            updatedGroups[this.currentGroupIndex].quoteTypeLabel = '单价*数量';
            updatedGroups[this.currentGroupIndex].quoteTypeValue = '1';


            // 设置默认值
            if (!updatedGroups[this.currentGroupIndex].minimumUnitPrice) {
                updatedGroups[this.currentGroupIndex].minimumUnitPrice = '';
            }
            if (!updatedGroups[this.currentGroupIndex].minimumQuantity) {
                updatedGroups[this.currentGroupIndex].minimumQuantity = '';
            }
            if (!updatedGroups[this.currentGroupIndex].expandMultiple) {
                updatedGroups[this.currentGroupIndex].expandMultiple = 1;
            }

            this.productGroups = updatedGroups;

            console.log('单价*数量报价方式添加完成');
        }
    }

    // 添加共享阶梯金额折扣落区
    handleAddSharedLadderAmountDiscountDownType() {
        console.log('添加共享阶梯金额折扣落区，产品组索引:', this.currentGroupIndex);
        
        if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 设置共享阶梯金额折扣落区标志
            updatedGroups[this.currentGroupIndex].hasSharedLadderAmountDiscountDown = true;
            updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
            updatedGroups[this.currentGroupIndex].quoteTypeValue = '共享阶梯金额折扣，落区计算';
            
            // 创建默认阶梯
            const emptyTier = this.createEmptyTier();
            emptyTier.unit = '金额';
            emptyTier.calculationMethod = '落区';
            emptyTier.lowerBound = '0';
            emptyTier.upperBound = '100000';
            emptyTier.discount = '90'; // 默认折扣90%
            emptyTier.unitNotEditable = true; // 单位不可编辑
            emptyTier.calculationMethodNotEditable = true; // 计算方式不可编辑
            emptyTier.isDeletable = true; // 第一个阶梯可删除
            
            // 如果没有阶梯，直接添加；如果有阶梯，添加一个新的
            if (updatedGroups[this.currentGroupIndex].tiers.length === 0) {
                updatedGroups[this.currentGroupIndex].tiers = [emptyTier];
            } else {
                // 先将所有现有阶梯设为不可删除
                updatedGroups[this.currentGroupIndex].tiers.forEach(tier => {
                    tier.isDeletable = false;
                });
                
                // 添加新阶梯
                updatedGroups[this.currentGroupIndex].tiers.push(emptyTier);
            }
            
            this.productGroups = updatedGroups;
            
            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '共享阶梯金额折扣落区报价方式已添加',
                    variant: 'success'
                })
            );
        }
    }

    // 添加共享阶梯用量单价落区
    handleAddSharedLadderUsagePriceDownType() {
        console.log('添加共享阶梯用量单价落区，产品组索引:', this.currentGroupIndex);
        
        if (this.currentGroupIndex !== null && this.currentGroupIndex >= 0 && this.currentGroupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            
            // 设置共享阶梯用量单价落区标志
            updatedGroups[this.currentGroupIndex].hasSharedLadderUsagePriceDown = true;
            updatedGroups[this.currentGroupIndex].hasAnyQuoteType = true;
            updatedGroups[this.currentGroupIndex].quoteTypeValue = '共享阶梯用量单价落区';

            // 设置扩大倍数默认值
            if (!updatedGroups[this.currentGroupIndex].expandMultiple) {
                updatedGroups[this.currentGroupIndex].expandMultiple = 1;
            }
            
            // 无论是否有阶梯，都创建一个新的空阶梯并设置默认值
            // 这样可以确保始终使用正确的默认值
            const emptyTier = this.createEmptyTier();
            emptyTier.unit = '用量';
            emptyTier.calculationMethod = '落区';
            emptyTier.lowerBound = '0';
            emptyTier.upperBound = '100000';
            emptyTier.discount = '90';
            emptyTier.unitNotEditable = true; // 单位不可编辑
            emptyTier.calculationMethodNotEditable = true; // 计算方式不可编辑
            emptyTier.isDeletable = true; // 第一个阶梯可删除
            
            // 如果没有阶梯，直接添加；如果有阶梯，添加一个新的
            if (updatedGroups[this.currentGroupIndex].tiers.length === 0) {
                updatedGroups[this.currentGroupIndex].tiers = [emptyTier];
            } else {
                // 先将所有现有阶梯设为不可删除
                updatedGroups[this.currentGroupIndex].tiers.forEach(tier => {
                    tier.isDeletable = false;
                });
                
                // 添加新阶梯
                updatedGroups[this.currentGroupIndex].tiers.push(emptyTier);
            }
            
            this.productGroups = updatedGroups;
            
            
            // 显示成功提示
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '共享阶梯用量单价落区报价方式已添加',
                    variant: 'success'
                })
            );
        }
    }

    // 删除所有报价方式
    handleDeleteAllQuoteTypes(event) {
        /* ---------------- 单产品弹窗删除 ---------------- */
        if (this.currentConfigSingleIndex !== null && this.currentConfigGroup) {
            const target = this.currentConfigGroup;
            const flagNames = [
                // 'hasFixedAmountQuote', // 注释掉固定金额报价方式
                'hasProductDiscountQuote','hasSharedLadderAmountDiscountZone',
                'hasSharedLadderAmountDiscountDown','hasSharedLadderUsagePriceDown',
                'hasMinAmountSharedLadderAmountDiscountDown','hasMinUnitPriceSharedLadderUsagePriceDown',
                'hasMinimumAmountQuote','hasMinimumUnitPriceQuote' // 添加保底UI标志
            ];
            flagNames.forEach(f=>target[f]=false);

            // 清空相关字段
            target.fixedDosage = null;
            target.fixedUnitPrice = null;
            // target.fixedAmount = null; // 注释掉固定金额字段
            target.discountCoefficient = null;
            target.fixedRebate = null;
            target.cashReduce = null;
            target.credit = null;
            target.minimumAmount = null;
            target.minimumUnitPrice = null;
            target.minimumQuantity = null;
            target.tiers = [];
            target.quoteTypeValue = '';
            target.hasAnyQuoteType = false;

            // 更新数组
            const updated = [...this.singleProducts];
            updated[this.currentConfigSingleIndex] = { ...target };
            this.singleProducts = updated;

            // 提示
            this.dispatchEvent(new ShowToastEvent({title:'成功',message:'已删除所有报价方式',variant:'success'}));
            return;
        }

        /* ---------------- 产品组删除 ---------------- */
        const groupIndex = parseInt(event.currentTarget.dataset.groupIndex, 10);
        console.log('删除所有报价方式，产品组索引:', groupIndex);
        
        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
             // 创建新的数组以确保响应式更新
             const updatedGroups = [...this.productGroups];
             
             // 清除所有报价方式标志
             updatedGroups[groupIndex].hasLadderQuote = false;
             updatedGroups[groupIndex].hasMinimumAmountQuote = false;
             updatedGroups[groupIndex].hasMinimumUnitPriceQuote = false;
             updatedGroups[groupIndex].hasFixedUsageQuote = false;
             updatedGroups[groupIndex].hasMaximumAmountQuote = false;
             updatedGroups[groupIndex].hasProductDiscountQuote = false;
             // updatedGroups[groupIndex].hasFixedAmountQuote = false; // 注释掉固定金额报价方式
             updatedGroups[groupIndex].hasSharedLadderAmountDiscountZone = false;
             updatedGroups[groupIndex].hasSharedLadderAmountDiscountDown = false;
             updatedGroups[groupIndex].hasSharedLadderUsagePriceDown = false;
             updatedGroups[groupIndex].hasMinAmountSharedLadderAmountDiscountDown = false;
             updatedGroups[groupIndex].hasMinUnitPriceSharedLadderUsagePriceDown = false;
             
             // 清除所有报价方式相关的数据
             updatedGroups[groupIndex].minimumAmount = null;
             updatedGroups[groupIndex].minimumUnitPrice = null;
             updatedGroups[groupIndex].minimumQuantity = null;
             updatedGroups[groupIndex].fixedUsage = null;
             // updatedGroups[groupIndex].fixedAmount = null; // 注释掉固定金额报价方式
             updatedGroups[groupIndex].maximumAmount = null;
             updatedGroups[groupIndex].discountCoefficient = null;
             updatedGroups[groupIndex].fixedRebate = null;
             updatedGroups[groupIndex].cashReduce = null;
             updatedGroups[groupIndex].credit = null;
             updatedGroups[groupIndex].fixedPrice = null;
             updatedGroups[groupIndex].fixedQuantity = null;
             
             // 清除阶梯数据
             updatedGroups[groupIndex].tiers = [];
             
             // 更新是否有任何报价方式的标志
             updatedGroups[groupIndex].hasAnyQuoteType = false;
             
             this.productGroups = updatedGroups;
             
             // 显示成功提示
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: '成功',
                     message: '所有报价方式已删除',
                     variant: 'success'
                 })
             );
         }
     }

    applyQuoteTypeFlags(target, type, typeLabel){
        // 重置所有报价方式标志
        const flagNames=[
            // 'hasFixedAmountQuote', // 注释掉固定金额报价方式
            'hasProductDiscountQuote','hasSharedLadderAmountDiscountZone',
            'hasSharedLadderAmountDiscountDown','hasSharedLadderUsagePriceDown',
            'hasMinAmountSharedLadderAmountDiscountDown','hasMinUnitPriceSharedLadderUsagePriceDown',
            'hasMinimumAmountQuote','hasMinimumUnitPriceQuote' // 添加保底UI标志
        ];
        flagNames.forEach(f=>target[f]=false);

        // 清空所有报价方式相关的数据字段，确保完全覆盖之前的报价方式
        target.fixedDosage = null;
        target.fixedUnitPrice = null;
        // target.fixedAmount = null; // 注释掉固定金额报价方式
        target.fixedUsage = null;
        target.discountCoefficient = null;
        target.fixedRebate = null;
        target.cashReduce = null;
        target.credit = null;
        target.minimumAmount = null;
        target.minimumUnitPrice = null;
        target.minimumQuantity = null;
        target.tiers = []; // 清空阶梯数据

        // 根据新选择的报价方式设置对应的标志
        switch(type){
            // case '固定金额':target.hasFixedAmountQuote=true;break; // 注释掉固定金额报价方式
            case '2':target.hasProductDiscountQuote=true;
                    target.discountType = '1';
                    target.discountTypeLabel = '产品折扣';break;
            case '1':target.hasMinimumUnitPriceQuote=true;break;
            case '4':
                target.hasSharedLadderAmountDiscountZone=true;      
                target.discountType = '1';
                target.discountTypeLabel = '产品折扣';
                break;
            // case '共享阶梯金额折扣，落区计算':target.hasSharedLadderAmountDiscountDown=true;break;
            case '3':target.hasSharedLadderUsagePriceDown=true;break;
           case '5':
                target.hasMinAmountSharedLadderAmountDiscountDown=true;
                target.hasMinimumAmountQuote=true;
                target.hasSharedLadderAmountDiscountDown=true; // 设置基础阶梯标志以显示阶梯
                break;
            case '6':
                target.hasMinUnitPriceSharedLadderUsagePriceDown=true;
                target.hasMinimumUnitPriceQuote=true;
                target.hasSharedLadderUsagePriceDown=true; // 设置基础阶梯标志以显示阶梯
                break;
        }
        target.quoteTypeValue = type;
        target.quoteTypeLabel = typeLabel;
        // 使用统一的方法检查是否有报价方式
        target.hasAnyQuoteType = this.checkSingleProductHasQuoteType(target);

        console.log('应用报价方式标志并清理数据:', type, 'hasAnyQuoteType:', target.hasAnyQuoteType);
    }
     // 为单产品初始化阶梯
    initializeTiersForSingleProduct(typeValue) {
        console.log('为单产品初始化阶梯:', typeValue);

        // 初始化阶梯数组
        if (!Array.isArray(this.currentConfigGroup.tiers)) {
            this.currentConfigGroup.tiers = [];
        }

        // 如果没有阶梯，添加一个默认阶梯
        if (this.currentConfigGroup.tiers.length === 0) {
            const defaultTier = this.createEmptyTier();

            // 根据报价方式设置默认值
            // if (typeValue.includes('金额')) {
            //     defaultTier.unit = '金额';
            // } else if (typeValue.includes('用量')) {
            //     defaultTier.unit = '用量';
            // }

            // if (typeValue.includes('分区')) {
            //     defaultTier.calculationMethod = '分区';
            // } else if (typeValue.includes('落区')) {
            //     defaultTier.calculationMethod = '落区';
            // }

            defaultTier.unitNotEditable = true;
            defaultTier.calculationMethodNotEditable = true;
            defaultTier.lowerBound = '0';
            defaultTier.upperBound = '100000';
            defaultTier.discount = '90';
            defaultTier.isDeletable = true;

            this.currentConfigGroup.tiers = [defaultTier];
        }

        // 初始化保底字段
        if (typeValue === '5') {
            if (!this.currentConfigGroup.minimumAmount) {
                this.currentConfigGroup.minimumAmount = '';
            }
        } else if (typeValue === '6') {
            if (!this.currentConfigGroup.minimumUnitPrice) {
                this.currentConfigGroup.minimumUnitPrice = '';
            }
            if (!this.currentConfigGroup.minimumQuantity) {
                this.currentConfigGroup.minimumQuantity = '';
            }
        }

        console.log('单产品阶梯初始化完成');
    }


    handleQuoteTypeChange(event) {
        const typeValue = event.target.value;
        const typeLabel = event.target.options.find(opt => opt.value === event.detail.value).label;
        console.log('typeLabel**',JSON.stringify(event.target.options));
        // 如果是在配置单产品
        if(this.currentConfigSingleIndex!==null){
            this.applyQuoteTypeFlags(this.currentConfigGroup,typeValue,typeLabel);
              // 为需要阶梯的报价方式初始化阶梯
            if(typeValue === '4' ||
               typeValue === '5' ||
               typeValue === '6' ||
               // typeValue === '共享阶梯金额折扣，落区计算' ||
               typeValue === '3') {
                this.initializeTiersForSingleProduct(typeValue);
            }
            return;
        }
        const groupIndex = parseInt(event.currentTarget.dataset.id, 10);
        console.log('选择的报价方式:', typeValue);
        if(groupIndex>=0 && groupIndex<this.productGroups.length){
            this.currentGroupIndex = groupIndex;
        }
        // if(typeValue === '固定金额') {
        //     this.handleAddFixedAmountQuoteType(); // 注释掉固定金额报价方式
        // }else
        if(typeValue === '2') {
            this.handleAddProductDiscountQuoteType();
        }else if(typeValue === '1') {
            this.handleAddMinimumUnitPriceQuoteType();
        }else if(typeValue === '4') {
            this.handleAddSharedLadderAmountDiscountZoneType();
        // }else if(typeValue === '共享阶梯金额折扣，落区计算') {
        //     this.handleAddSharedLadderAmountDiscountDownType();
        }else if(typeValue === '3') {
            this.handleAddSharedLadderUsagePriceDownType();
        }else if(typeValue === '5') {
            this.handleAddMinAmountSharedLadderAmountDiscountDownType();
        }else if(typeValue === '6') {
            this.handleAddMinUnitPriceSharedLadderUsagePriceDownType();
        }
    }
    handleBack(){
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: this.recordId,
                //objectApiName: 'Case', // objectApiName is optional
                actionName: 'view'
            }
        }); 
    }

    handleConfigureQuote(event) {
        const groupIndex = event.target.dataset.groupIndex;
        console.log('配置报价方式，产品组索引:', groupIndex);
        this.currentConfigGroupIndex = groupIndex;
        // 重置单产品索引，确保不会误判为单产品配置
        this.currentConfigSingleIndex = null;

        this.currentConfigGroup = this.productGroups[groupIndex];
        this.currentConfigGroupNumber = this.currentConfigGroup.groupNumber;

        // 确保是否赠送字段有默认值
        if (!this.currentConfigGroup.isGift) {
            this.currentConfigGroup.isGift = '2'; // 默认为否
            this.currentConfigGroup.isGiftLabel = '否';
        }

        this.showQuoteConfigModal = true;
    }

    closeQuoteConfigModal() {
        this.showQuoteConfigModal = false;
        // 重置所有上下文索引
        this.currentConfigGroupIndex = null;
        this.currentConfigSingleIndex = null;
        this.currentConfigGroup = null;
        this.currentConfigGroupNumber = null;
    }

    handleSaveQuoteConfig() {
        // 如果是在配置单产品
        if (this.currentConfigSingleIndex !== null && this.currentConfigGroup) {
            const updated = [...this.singleProducts];
            // 重新计算 hasAnyQuoteType
            const hasAnyQuoteType = this.checkSingleProductHasQuoteType(this.currentConfigGroup);

            // 设置保底类型显示标签
            if (this.currentConfigGroup.minimumGuaranteeType) {
                this.currentConfigGroup.minimumGuaranteeTypeLabel = this.getMinimumGuaranteeTypeLabel(this.currentConfigGroup.minimumGuaranteeType);
            }

            // 设置折扣类型显示标签
            if (this.currentConfigGroup.discountType) {
                this.currentConfigGroup.discountTypeLabel = this.getDiscountTypeLabel(this.currentConfigGroup.discountType);
            }

            // 设置是否赠送显示标签
            if (this.currentConfigGroup.isGift) {
                this.currentConfigGroup.isGiftLabel = this.currentConfigGroup.isGift === '1' ? '是' : '否';
            }

            updated[this.currentConfigSingleIndex] = {
                ...this.currentConfigGroup,
                hasAnyQuoteType: hasAnyQuoteType
            };
            this.singleProducts = updated;

            console.log('单产品报价配置已保存，hasAnyQuoteType:', hasAnyQuoteType);
        }
        // 如果是在配置产品组（保持原有逻辑）
        else if (this.currentConfigGroupIndex !== null && this.currentConfigGroup) {
            const updated = [...this.productGroups];

            // 设置保底类型显示标签
            if (this.currentConfigGroup.minimumGuaranteeType) {
                this.currentConfigGroup.minimumGuaranteeTypeLabel = this.getMinimumGuaranteeTypeLabel(this.currentConfigGroup.minimumGuaranteeType);
            }

            // 设置折扣类型显示标签
            if (this.currentConfigGroup.discountType) {
                this.currentConfigGroup.discountTypeLabel = this.getDiscountTypeLabel(this.currentConfigGroup.discountType);
            }

            // 设置是否赠送显示标签
            if (this.currentConfigGroup.isGift) {
                this.currentConfigGroup.isGiftLabel = this.currentConfigGroup.isGift === '1' ? '是' : '否';
            }

            updated[this.currentConfigGroupIndex] = this.currentConfigGroup;
            this.productGroups = updated;
        }

        // 关闭弹窗并重置上下文
        this.showQuoteConfigModal = false;
        this.currentConfigGroupIndex = null;
        this.currentConfigSingleIndex = null;
        this.currentConfigGroup = null;
        this.currentConfigGroupNumber = null;
    }

    // 加载单产品数据
    loadSingleProducts() {
        console.log('加载单产品数据，报价ID:', this.recordId);

        if (!this.recordId) {
            console.log('报价ID为空，初始化空单产品列表');
            this.singleProducts = [];
            // 清空选中状态
            this.selectedSingleProducts = [];
            return;
        }

        this.isLoading = true;

        // 保存当前选中的产品ID
        const currentSelectedIds = this.selectedSingleProducts.map(product => product.id);

        // 查询单产品数据（ISGROUP__c = false的QuoteLineItem）
        // 使用新的 getSingleProducts 方法获取单产品数据
        getSingleProducts({
            quoteId: this.recordId
        })
        .then(data => {
            console.log('获取单产品数据成功:', JSON.stringify(data));
            if (data && data.length > 0) {
                this.singleProducts = this.processSingleProductData(data);
                console.log('this.singleProducts:', JSON.stringify(this.singleProducts));

                // 更新单产品的三级产品标签
                this.updateSingleProductTags();

                // 恢复选中状态
                if (currentSelectedIds.length > 0) {
                    this.selectedSingleProducts = this.singleProducts.filter(product =>
                        currentSelectedIds.includes(product.id)
                    );
                    console.log('恢复选中状态，选中产品数:', this.selectedSingleProducts.length);

                    // 延迟刷新选中状态，确保DOM已更新
                    setTimeout(() => {
                        this.refreshSingleProductSelection();
                    }, 100);
                }
            } else {
                this.singleProducts = [];
                this.selectedSingleProducts = [];
            }
        })
        .catch(error => {
            console.error('获取单产品数据失败:', this.reduceErrors(error));
            this.showError('获取单产品数据失败: ' + this.reduceErrors(error));
            this.singleProducts = [];
            this.selectedSingleProducts = [];
        })
        .finally(() => {
            this.isLoading = false;
        });
    }
    
    // 处理单产品数据
    processSingleProductData(data) {
        // 根据是否为MaaS产品决定是否显示复制按钮
        const actions= this.isEditMode ? [
                    { label: '报价方式', name: 'quote_type' },
                    ...(this.isMassProduct ? [] : [{ label: '复制', name: 'clone' }]),
                    ...(this.isMspProduct ? [{ label: '配置客户账号id', name: 'configure_customer_ids' }]:[] ),
                ] : [
                    { label: '报价方式', name: 'quote_type' },
                    // { label: '配置客户账号id', name: 'configure_customer_ids' }
                ]

        // 处理单产品的三级产品标签
        const processedProducts = this.processProductData(data);
        const maasLevelThreeProducts = processedProducts.filter(p => p.level1 === 'MaaS');
        const levelThreeProductNames = [...new Set(maasLevelThreeProducts.map(p => p.level3).filter(Boolean))];

        // 处理三级产品标签显示逻辑
        const allLevelThreeProducts = levelThreeProductNames.map(name => ({ name, id: 'single_' + name }));
        const displayedLevelThreeProducts = allLevelThreeProducts.slice(0, 4).map(product => ({ ...product, shouldDisplay: true }));
        const hasMoreTags = allLevelThreeProducts.length > 4;
        const remainingCount = Math.max(0, allLevelThreeProducts.length - 4);
        const moreTagsLabel = `更多 (${remainingCount})`;

        // 为单产品添加标签相关属性
        this.singleProductTags = {
            levelThreeProducts: displayedLevelThreeProducts,
            allLevelThreeProducts: allLevelThreeProducts,
            hasMoreTags: hasMoreTags,
            moreTagsLabel: moreTagsLabel
        };

        return data.map((product, index) => {
            const processedProduct = {
                ...product,
                id: product.id || ('temp_single_product_' + Date.now() + '_' + index),
                productName: product.productName || '未知产品',
                exchangeRateDifferenceLabel: this.exchangeRateDifference,
                exchangeRateDifferenceValue: this.quoteInfo?.exchangeRateDifferenceValue || '2',
                dynamicActions: actions

            };

            // 根据组合报价方式设置UI标志和报价方式名称
            if (processedProduct.hasMinAmountSharedLadderAmountDiscountDown) {
                processedProduct.hasMinimumAmountQuote = true;
                processedProduct.hasSharedLadderAmountDiscountDown = true;
                // 处理保底类型
                processedProduct.minimumGuaranteeType = processedProduct.minimumGuaranteeType || '1';
                processedProduct.minimumGuaranteeTypeLabel = this.getMinimumGuaranteeTypeLabel(processedProduct.minimumGuaranteeType);
                // processedProduct.quoteTypeValue = '保底金额+共享阶梯金额折扣落区';
            }

            if (processedProduct.hasMinUnitPriceSharedLadderUsagePriceDown) {
                processedProduct.hasMinimumUnitPriceQuote = true;
                processedProduct.hasSharedLadderUsagePriceDown = true;
                // processedProduct.quoteTypeValue = '单价*数量+共享阶梯用量单价落区';
            }

            // 确保 hasAnyQuoteType 正确设置
            processedProduct.hasAnyQuoteType = this.checkSingleProductHasQuoteType(processedProduct);

            // 确保阶梯类型和折扣类型有默认值
            if (!processedProduct.ladderType) {
                processedProduct.ladderType = '01'; // 默认为落区
            }
            if (!processedProduct.discountType) {
                processedProduct.discountType = '1'; // 默认为产品折扣
            }

            // 处理阶梯数据，确保 isDeletable 属性正确设置
            if (Array.isArray(processedProduct.tiers) && processedProduct.tiers.length > 0) {
                processedProduct.tiers = processedProduct.tiers.map((tier, tierIndex, tierArray) => {
                    return {
                        ...tier,
                        isDeletable: tierIndex === (tierArray.length - 1) // 只有最后一个阶梯可删除
                    };
                });
            }

            console.log('处理单产品数据:', processedProduct.Name, 'hasAnyQuoteType:', processedProduct.hasAnyQuoteType, '阶梯数量:', processedProduct.tiers ? processedProduct.tiers.length : 0);

            return processedProduct;
        });
    }
    
    // 创建空单产品
    createEmptySingleProduct() {
        // 计算汇率差系数
        let unitPrice = 1.0; // 默认值
        if (this.isMspProduct && this.quoteInfo?.exchangeRateDifferenceValue === '1') {
            // MSP产品线且存在汇率差时，设置为1.02
            unitPrice = 1.02;
        }

        return {
            id: 'temp_single_product_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            productId: '',
            productName: '未知产品',
            ProductCode: '',
            Name: '',
            Family: '',
            Description: '',
            ProductDescription: '',
            QuantityUnitOfMeasure: '',
            taxRate: '',
            unitPrice: unitPrice,
            customerAccountId: this.quoteInfo?.Opportunity?.Account_ID__c,
            profitDescription: '',
            Region: '',
            OneTimeFee: '否',
            chargeType: '计费',
            QuoteLineStartDate: this.quoteInfo?.startDate || null,
            QuoteLineEndDate: this.quoteInfo?.endDate || null,
            quoteTypeValue: '',
            // 报价方式标志 - 七种报价方式
            // hasFixedAmountQuote: false,                        // 固定金额 - 已注释掉
            hasProductDiscountQuote: false,                       // 产品折扣
            hasSharedLadderAmountDiscountZone: false,            // 共享阶梯金额折扣分区
            hasSharedLadderAmountDiscountDown: false,            // 共享阶梯金额折扣落区
            hasSharedLadderUsagePriceDown: false,                // 共享阶梯用量单价落区
            hasMinAmountSharedLadderAmountDiscountDown: false,   // 保底金额+共享阶梯金额折扣落区
            hasMinUnitPriceSharedLadderUsagePriceDown: false,    // 单价*数量+共享阶梯用量单价落区
            // 报价方式相关数据
            fixedDosage: null,
            fixedUnitPrice: null,
            discountCoefficient: null,
            fixedRebate: null,
            cashReduce: null,
            credit: null,
            discountType: '1', // 默认为产品折扣
            discountTypeLabel: '产品折扣',
            ladderType: '01', // 默认阶梯类型为落区
            minimumAmount: null,
            minimumGuaranteeType: '1', // 默认为折前
            minimumGuaranteeTypeLabel: '折前',
            minimumUnitPrice: null,
            minimumQuantity: null,
            expandMultiple: 1, // 扩大倍数字段，默认值为1
            // 阶梯数据
            tiers: []
        };
    }
    
    // 处理添加单产品按钮点击
    handleAddSingleProduct() {
        console.log('添加单产品');
        // 使用与产品组添加产品相同的逻辑
        this.isModalOpen = true;
        this.groupIndex = 'single'; // 使用特殊标识符表示是单产品
    }
    
    // 处理删除单产品按钮点击
    handleDeleteSingleProduct() {
        console.log('删除选中的单产品');

        if (!this.selectedSingleProducts || this.selectedSingleProducts.length === 0) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '提示',
                    message: '请先选择要删除的产品',
                    variant: 'info'
                })
            );
            return;
        }

        // 获取选中行的ID
        const selectedIds = this.selectedSingleProducts.map(row => row.id);
        console.log('要删除的产品IDs:', selectedIds);

        // 从临时单产品列表中删除临时产品
        selectedIds.forEach(productId => {
            if (productId && productId.startsWith('temp_')) {
                this.removeTempSingleProduct(productId);
            }
        });

        // 过滤掉选中的行
        this.singleProducts = this.singleProducts.filter(
            product => !selectedIds.includes(product.id)
        );

        // 清空选中行
        this.selectedSingleProducts = [];

        // 显示成功提示
        this.dispatchEvent(
            new ShowToastEvent({
                title: '成功',
                message: `已删除 ${selectedIds.length} 个产品`,
                variant: 'success'
            })
        );
    }

    // 从临时单产品列表中删除指定产品
    removeTempSingleProduct(productId) {
        console.log('从临时单产品列表删除产品:', productId);

        // 从临时单产品列表中删除
        this.tempSingleProducts = this.tempSingleProducts.map(tempData => {
            const updatedProducts = tempData.products.filter(product => product.id !== productId);
            return {
                ...tempData,
                products: updatedProducts
            };
        }).filter(tempData => tempData.products.length > 0); // 移除空的临时数据

        console.log('临时单产品列表更新完成');
    }
    
    // 处理单产品行选择事件
    handleSingleProductRowSelection(event) {
        // 修复：确保正确处理选中行的响应式更新
        const selectedRows = event.detail.selectedRows;
        console.log('单产品选择事件触发，选中行数:', selectedRows.length);
        console.log('选中的单产品详情:', JSON.stringify(selectedRows));
        console.log('当前编辑模式:', this.isEditMode);
        console.log('复选框列隐藏状态:', this.hideCheckboxColumn);

        // 确保selectedSingleProducts数组存在
        if (!this.selectedSingleProducts) {
            this.selectedSingleProducts = [];
        }

        // 使用扩展运算符确保响应式更新
        this.selectedSingleProducts = [...selectedRows];

        console.log('更新后的selectedSingleProducts:', JSON.stringify(this.selectedSingleProducts));
        console.log('更新后的selectedSingleProductIds:', this.selectedSingleProductIds);

        // 强制触发响应式更新 - 这是关键！
        // 通过重新赋值来触发getter的重新计算
        this.selectedSingleProducts = this.selectedSingleProducts.slice();

        // 延迟一点时间确保DOM更新
        // setTimeout(() => {
        //     console.log('延迟检查 - selectedSingleProductIds:', this.selectedSingleProductIds);
        // }, 50);
    }

    // 手动刷新单产品datatable的选中状态
    refreshSingleProductSelection() {
        const datatable = this.template.querySelector('c-custom-datatable');
        if (datatable) {
            console.log('刷新选中状态，当前选中ID数组:', this.selectedSingleProductIds);

            // 设置选中行
            datatable.selectedRows = this.selectedSingleProductIds;

            // 强制触发重新渲染
            datatable.selectedRows = [...this.selectedSingleProductIds];
        }
    }

    // 调试方法：检查复选框状态
    // debugCheckboxState() {
    //     console.log('=== 复选框状态调试 ===');
    //     console.log('当前编辑模式:', this.isEditMode);
    //     console.log('复选框列隐藏状态:', this.hideCheckboxColumn);
    //     console.log('选中的单产品数量:', this.selectedSingleProducts.length);
    //     console.log('选中的单产品ID数组:', this.selectedSingleProductIds);
    //     console.log('选中的单产品对象:', JSON.stringify(this.selectedSingleProducts));
    //     console.log('单产品总数:', this.singleProducts.length);

    //     const datatable = this.template.querySelector('c-custom-datatable');
    //     if (datatable) {
    //         console.log('Datatable组件存在');
    //         console.log('Datatable selectedRows属性:', datatable.selectedRows);
    //         console.log('Datatable hideCheckboxColumn属性:', datatable.hideCheckboxColumn);
    //         console.log('Datatable data长度:', datatable.data ? datatable.data.length : 'undefined');

    //         // 测试：手动选中前两个产品
    //         if (this.singleProducts.length >= 2) {
    //             const testSelection = this.singleProducts.slice(0, 2);
    //             this.selectedSingleProducts = [...testSelection];
    //             console.log('测试选中前两个产品ID:', testSelection.map(p => p.id));
    //             console.log('更新后的selectedSingleProductIds:', this.selectedSingleProductIds);

    //             // 强制刷新datatable
    //             setTimeout(() => {
    //                 this.refreshSingleProductSelection();
    //                 console.log('刷新后的datatable selectedRows:', datatable.selectedRows);
    //             }, 100);
    //         }
    //     } else {
    //         console.log('Datatable组件不存在');
    //     }
    //     console.log('=== 调试结束 ===');
    // }
    handleSingleProductChange(event) {
        const draftValues = event.detail.draftValues;
        // draftValues 包含修改的数据
        console.log('Changed values:', JSON.stringify(draftValues));
        
        // 处理修改的数据
        draftValues.forEach(draft => {
            const index = this.singleProducts.findIndex(item => item.id === draft.id);
            if (index !== -1) {
                // 更新对应字段
                Object.assign(this.singleProducts[index], draft);
            }
        });
        
        // 通知父组件数据已更改
        this.dispatchEvent(new CustomEvent('valuechange', {
            detail: {
                products: this.singleProducts
            }
        }));
    }
    // 处理单产品单元格值更改
    async handleSingleProductCellChange(event) {
        const draftValues = event.detail.draftValues;
        console.log('*******测试********:', JSON.stringify(draftValues));
        if (!draftValues || draftValues.length === 0) {
            return;
        }
        
        console.log('单产品单元格编辑值:', JSON.stringify(draftValues));
        
        // 创建新的数组以确保响应式更新
        const updatedProducts = [...this.singleProducts];
        const datatable = event.target;
        
        // 验证所有必填字段是否已填写
        let hasError = false;
        
        // 处理每个编辑过的单元格
        for (let i = 0; i < draftValues.length; i++) {
            const draftValue = draftValues[i];
            const productId = draftValue.id;
            const productObj =  updatedProducts.find(product => product.id === productId);
             

            //牌价查询（仅MaaS和AI Search产品线）
            if ('Region' in draftValue && (draftValue.Region != null && draftValue.Region != undefined && draftValue.Region != '')) {
                // 只有MaaS和AI Search产品线才查询牌价
                if (this.shouldQueryListPrice) {
                    await getProductAndAreaListPrice({
                        productId: productObj.productId,
                        areaStr:draftValue.Region,
                        quoteId:this.recordId
                    }).then(result => {
                        console.log('单产品 getProductAndAreaListPrice res:',JSON.stringify(result));
                        if(result.isSuccess !='true'){
                            // 显示错误提示
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    title: '错误',
                                    message: result.msg,
                                    variant: 'error'
                                })
                            );
                            draftValue.Region='';
                            hasError = true;
                            // break;
                        }else{
                            draftValue.listPrice=result.amount;
                            draftValue.listPriceCurrency=result.currencyCode;
                            draftValue.pricingId = result.pricing;
                        }


                    }).catch(error => {
                        console.error('查询牌价:', this.reduceErrors(error));
                        this.showError('查询牌价: ' + this.reduceErrors(error));
                        // this.initializeEmptyGroups();
                    })
                } else {
                    console.log('当前产品线不需要查询牌价，仅更新区域信息');
                }
            }
            
            // 验证利润率字段是否已填写
            if ('taxRate' in draftValue && (draftValue.taxRate === null || draftValue.taxRate === undefined || draftValue.taxRate === '')) {
                // 显示错误提示
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: '错误',
                        message: '利润率是必填字段，请输入有效的利润率值',
                        variant: 'error'
                    })
                );
                hasError = true;
                break;
            }
            
            // 查找产品在数组中的索引
            const productIndex = updatedProducts.findIndex(product => product.id === productId);
            
            if (productIndex !== -1) {
                // 获取所有更改的字段
                console.log('单产品单元格编辑更新后:', JSON.stringify(draftValue));
                Object.keys(draftValue).forEach(field => {
                    if (field !== 'id') { // 允许编辑基准价字段
                        // 更新字段值
                        updatedProducts[productIndex][field] = draftValue[field];
                        console.log(`更新单产品字段，产品索引: ${productIndex}, 字段: ${field}, 值: ${draftValue[field]}`);
                    }
                });
            }
        }
        
        if (hasError) {
            // 如果有错误，不更新数据
            return;
        }else{
            // 更新数据
            this.singleProducts = updatedProducts;
            
            // 清除草稿值，确保表格恢复到查看模式
            if (datatable) {
                datatable.draftValues = [];
            }
            
            // 显示成功消息
            this.dispatchEvent(
                new ShowToastEvent({
                    title: '成功',
                    message: '产品数据已更新',
                    variant: 'success'
                })
            );

        }
        
        
    }
    handleSingleProductCellCancel(event){
        this.singleProducts =[...this.singleProducts];
        this.draftValues = [];

    }
    
    // 处理单产品行操作
    handleRowAction(event) {
        const action = event.detail.action;
        const row = event.detail.row;

        console.log('行操作:', action.name, '行数据:', JSON.stringify(row));

        switch (action.name) {
            case 'quote_type':
                this.handleConfigureSingleProductQuote(row);
                break;
            case 'clone':
                this.handleCloneSingleProduct(row);
                break;
            case 'configure_customer_ids':
                this.handleConfigureCustomerIds(row);
                break;
            default:
                break;
        }
    }

    // 处理产品组行操作
    handleProductGroupRowAction(event) {
        const action = event.detail.action;
        const row = event.detail.row;

        console.log('产品组行操作:', action.name, '行数据:', JSON.stringify(row));

        switch (action.name) {
            case 'ConfigGroupProductCustAccountID':
                this.handleConfigureProductGroupCustomerIds(row, event);
                break;
            default:
                break;
        }
    }
    
    // 处理配置单产品报价方式
    handleConfigureSingleProductQuote(product) {
        console.log('配置单产品报价方式:', JSON.stringify(product));

        // 查找产品在数组中的索引
        const productIndex = this.singleProducts.findIndex(p => p.id === product.id);

        if (productIndex !== -1) {
            // 设置当前操作的单产品索引标识
            this.currentConfigSingleIndex = productIndex;
            // 重置产品组索引，确保不会误判为产品组配置
            this.currentConfigGroupIndex = null;
            // 将当前单产品复制到弹窗数据对象，保持与产品组弹窗一致的结构
            this.currentConfigGroup = { ...this.singleProducts[productIndex] };

            // 确保扩大倍数有默认值
            if (!this.currentConfigGroup.expandMultiple) {
                this.currentConfigGroup.expandMultiple = 1;
            }

            // 确保是否赠送字段有默认值
            if (!this.currentConfigGroup.isGift) {
                this.currentConfigGroup.isGift = '2'; // 默认为否
                this.currentConfigGroup.isGiftLabel = '否';
            }

            // 确保 hasAnyQuoteType 正确设置
            this.currentConfigGroup.hasAnyQuoteType = this.checkSingleProductHasQuoteType(this.currentConfigGroup);

            // 处理阶梯数据，确保 isDeletable 属性正确设置
            if (Array.isArray(this.currentConfigGroup.tiers) && this.currentConfigGroup.tiers.length > 0) {
                this.currentConfigGroup.tiers = this.currentConfigGroup.tiers.map((tier, index, array) => {
                    return {
                        ...tier,
                        isDeletable: index === (array.length - 1) // 只有最后一个阶梯可删除
                    };
                });
            }

            // 单产品显示名称作为标题
            this.currentConfigGroupNumber = this.currentConfigGroup.Name || this.currentConfigGroup.productName || ('单产品' + (productIndex + 1));
            this.showQuoteConfigModal = true;
        }
    }

    // 检查单产品是否有报价方式
    checkSingleProductHasQuoteType(product) {
        // 注释掉固定金额报价方式: product.hasFixedAmountQuote === true ||
        return product.hasProductDiscountQuote === true ||
               product.hasMinimumUnitPriceQuote === true ||
               product.hasSharedLadderAmountDiscountZone === true ||
               product.hasSharedLadderAmountDiscountDown === true ||
               product.hasSharedLadderUsagePriceDown === true ||
               product.hasMinAmountSharedLadderAmountDiscountDown === true ||
               product.hasMinUnitPriceSharedLadderUsagePriceDown === true;
    }
    
    // 处理复制单产品
    handleCloneSingleProduct(product) {
        console.log('复制单产品:', JSON.stringify(product));
        
        // 创建产品的副本
        const clonedProduct = { ...product };
        
        // 生成新的ID
        clonedProduct.id = 'temp_single_product_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        clonedProduct.Region = product.Region;
        clonedProduct.OneTimeFee = product.OneTimeFee;
        clonedProduct.chargeType = product.chargeType;
        
        // 添加到单产品列表
        this.singleProducts = [...this.singleProducts, clonedProduct];

        // 更新单产品的三级产品标签
        this.updateSingleProductTags();

        // 显示成功提示
        this.dispatchEvent(
            new ShowToastEvent({
                title: '成功',
                message: '产品已复制',
                variant: 'success'
            })
        );
    }

    handleRemoveLevelThree(event) {
        this.groupIndexForDeletion = event.target.dataset.groupIndex;
        this.level3NameToDelete = event.target.dataset.level3Name;
        this.showDeleteConfirmationModal = true;
    }

    handleConfirmDeletion() {
        this.isLoading = true;
        console.log('handleConfirmDeletion');
        const group = this.productGroups[this.groupIndexForDeletion];
        console.log('handleConfirmDeletion2',JSON.stringify(group));

        // 过滤掉被删除的产品
        this.productGroups[this.groupIndexForDeletion].products = group.products.filter(product => product.level3 != this.level3NameToDelete);

        // 重新计算三级产品标签
        const remainingProducts = this.productGroups[this.groupIndexForDeletion].products;
        const processedProducts = Array.isArray(remainingProducts) ? this.processProductData(remainingProducts) : [];
        const maasLevelThreeProducts = processedProducts.filter(p => p.level1 === 'MaaS');
        const levelThreeProductNames = [...new Set(maasLevelThreeProducts.map(p => p.level3).filter(Boolean))];

        // 处理三级产品标签显示逻辑
        const allLevelThreeProducts = levelThreeProductNames.map(name => ({ name, id: (group.id || 'temp_group') + '_' + name }));
        const displayedLevelThreeProducts = allLevelThreeProducts.slice(0, 4).map(product => ({ ...product, shouldDisplay: true }));
        const hasMoreTags = allLevelThreeProducts.length > 4;
        const remainingCount = Math.max(0, allLevelThreeProducts.length - 4);
        const moreTagsLabel = `更多 (${remainingCount})`;

        this.productGroups[this.groupIndexForDeletion].levelThreeProducts = displayedLevelThreeProducts;
        this.productGroups[this.groupIndexForDeletion].allLevelThreeProducts = allLevelThreeProducts;
        this.productGroups[this.groupIndexForDeletion].hasMoreTags = hasMoreTags;
        this.productGroups[this.groupIndexForDeletion].moreTagsLabel = moreTagsLabel;

        console.log('handleConfirmDeletion3');
        this.productGroups =  [...this.productGroups];
        console.log('this.productGroups',JSON.stringify(this.productGroups));

        // this.loadProductGroups(); 

        this.closeConfirmationModal();
        this.isLoading = false;
        // deleteProductsByLevelThreeName({
        //     productGroupId: group.ladderDiscountId,
        //     levelThreeName: this.level3NameToDelete
        // })
        // .then(() => {
        //     this.dispatchEvent(
        //         new ShowToastEvent({
        //             title: '成功',
        //             message: '成功删除产品',
        //             variant: 'success'
        //         })
        //     );
        //     this.loadProductGroups(); 
        // })
        // .catch(error => {
        //     this.showError(this.reduceErrors(error));
        // })
        // .finally(() => {
        //     this.closeConfirmationModal();
        //     this.isLoading = false;
        // });
    }

    closeConfirmationModal() {
        this.showDeleteConfirmationModal = false;
        this.groupIndexForDeletion = null;
        this.level3NameToDelete = '';
    }

    // 计算当前配置对象的标题（产品组或单产品）
    get currentConfigGroupLabel() {
        // 如果是在配置单产品
        if (this.currentConfigSingleIndex !== null) {
            return this.currentConfigGroupNumber ? this.currentConfigGroupNumber : '单产品';
        }
        // 产品组
        return '组合' + this.currentConfigGroupNumber;
    }

    handleladderTypeChange(event){
        this.currentConfigGroup.ladderType = event.target.value;
    }

    // 处理保底类型变更（配置弹窗中）
    handleMinimumGuaranteeTypeChange(event) {
        // 如果是在配置弹窗中
        if (this.currentConfigGroup) {
            this.currentConfigGroup.minimumGuaranteeType = event.target.value;
            console.log('配置弹窗中保底类型变更:', event.target.value);
            return;
        }

        // 如果是在产品组视图中
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        const value = event.target.value;
        console.log(`产品组保底类型变更，产品组索引: ${groupIndex}, 值: ${value}`);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex].minimumGuaranteeType = value;

            // 设置显示标签
            const selectedOption = this.minimumGuaranteeTypeOptions.find(option => option.value === value);
            if (selectedOption) {
                updatedGroups[groupIndex].minimumGuaranteeTypeLabel = selectedOption.label;
            }

            this.productGroups = updatedGroups;
        }
    }

    // 处理折扣类型变更
    handleDiscountTypeChange(event) {
        // 如果是在配置弹窗中
        if (this.currentConfigGroup) {
            this.currentConfigGroup.discountType = event.target.value;

            // 设置显示标签
            const selectedOption = this.discountTypeOptions.find(option => option.value === event.target.value);
            if (selectedOption) {
                this.currentConfigGroup.discountTypeLabel = selectedOption.label;
            }

            console.log('配置弹窗中折扣类型变更:', event.target.value);
            return;
        }

        // 如果是在产品组视图中
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        const value = event.target.value;
        console.log(`产品组折扣类型变更，产品组索引: ${groupIndex}, 值: ${value}`);

        if (groupIndex >= 0 && groupIndex < this.productGroups.length) {
            // 创建新的数组以确保响应式更新
            const updatedGroups = [...this.productGroups];
            updatedGroups[groupIndex].discountType = value;

            // 设置显示标签
            const selectedOption = this.discountTypeOptions.find(option => option.value === value);
            if (selectedOption) {
                updatedGroups[groupIndex].discountTypeLabel = selectedOption.label;
            }

            this.productGroups = updatedGroups;
        }
    }

    // 处理配置客户账号id
    handleConfigureCustomerIds(row) {
        console.log('配置客户账号id:', JSON.stringify(row));

        // 设置当前配置的数据
        this.currentCustomerAccountId = row.customerAccountId || '';
        this.currentProductId = row.id;

        // 清空产品组索引，表示这是单产品
        this.currentProductGroupIndex = null;

        // 显示客户账号id配置模态框
        this.showCustomerIdsModal = true;
    }

    // 处理客户账号id保存
    handleCustomerIdsSave(event) {
        const { productId, customerAccountId } = event.detail;

        console.log('保存客户账号id:', productId, customerAccountId);

        // 判断是单产品还是产品组中的产品
        if (this.currentProductGroupIndex !== null) {
            // 更新产品组中对应产品的客户账号id
            const updatedGroups = [...this.productGroups];
            updatedGroups[this.currentProductGroupIndex].products = updatedGroups[this.currentProductGroupIndex].products.map(product => {
                if (product.id === productId) {
                    return { ...product, customerAccountId: customerAccountId };
                }
                return product;
            });
            this.productGroups = updatedGroups;
        } else {
            // 更新单产品列表中对应产品的客户账号id
            this.singleProducts = this.singleProducts.map(product => {
                if (product.id === productId) {
                    return { ...product, customerAccountId: customerAccountId };
                }
                return product;
            });
        }

        // 关闭模态框
        this.showCustomerIdsModal = false;
        this.currentCustomerAccountId = '';
        this.currentProductId = '';
        this.currentProductGroupIndex = null;

        // 显示成功提示
        this.dispatchEvent(
            new ShowToastEvent({
                title: '成功',
                message: '客户账号ID配置已更新',
                variant: 'success'
            })
        );
    }

    // 处理客户账号id配置取消
    handleCustomerIdsCancel() {
        this.showCustomerIdsModal = false;
        this.currentCustomerAccountId = '';
        this.currentProductId = '';
        this.currentProductGroupIndex = null;
    }

    // 处理产品组中产品的客户账号id配置
    handleConfigureProductGroupCustomerIds(row, event) {
        console.log('配置产品组客户账号id:', JSON.stringify(row));

        // 获取产品组索引
        const groupIndex = event.target.closest('[data-group-index]')?.dataset.groupIndex;
        if (groupIndex === undefined) {
            // 如果无法从DOM获取，尝试从datatable的data-id属性获取
            const datatableElement = event.target.closest('c-custom-datatable');
            if (datatableElement) {
                const groupId = datatableElement.dataset.id;
                const foundGroupIndex = this.productGroups.findIndex(group => group.id === groupId);
                if (foundGroupIndex !== -1) {
                    this.currentProductGroupIndex = foundGroupIndex;
                }
            }
        } else {
            this.currentProductGroupIndex = parseInt(groupIndex, 10);
        }

        // 设置当前配置的数据
        this.currentCustomerAccountId = row.customerAccountId || '';
        this.currentProductId = row.id;

        // 显示客户账号id配置模态框
        this.showCustomerIdsModal = true;
    }
     // 计算并更新合同收入
    calculateContractRevenue() {
        if (!this.recordId) {
            console.log('报价ID为空，跳过合同收入计算');
            return;
        }
        console.log('开始计算合同收入，报价ID:', this.recordId);

        calculateAndUpdateContractRevenue({ quoteId: this.recordId })
            .then(result => {
                console.log('合同收入计算结果:', result);
                if (result.success) {
                    console.log('合同收入计算成功，总收入:', result.totalRevenue);
                } else {
                    console.error('合同收入计算失败:', result.message);   
                }
            })
            .catch(error => {
                console.error('合同收入计算异常:', error);
            });
    }

    // 处理"更多"标签按钮点击
    handleShowMoreTags(event) {
        const groupIndex = parseInt(event.target.dataset.groupIndex, 10);
        this.currentGroupForMoreTags = this.productGroups[groupIndex];
        this.showMoreTagsModal = true;
        console.log('显示更多标签弹窗，产品组:', this.currentGroupForMoreTags);
    }

    // 关闭"更多"标签弹窗
    closeMoreTagsModal() {
        this.showMoreTagsModal = false;
        this.currentGroupForMoreTags = null;
    }

    // 从"更多"标签弹窗中删除三级产品
    handleRemoveLevelThreeFromModal(event) {
        this.level3NameToDelete = event.target.dataset.level3Name;

        // 检查是否是单产品的标签
        if (this.currentGroupForMoreTags && !this.currentGroupForMoreTags.id) {
            // 这是单产品的标签删除
            this.handleRemoveLevelThreeFromSingle(event);
            this.showMoreTagsModal = false; // 关闭更多标签弹窗
        } else {
            // 这是产品组的标签删除
            this.groupIndexForDeletion = this.productGroups.findIndex(group => group.id === this.currentGroupForMoreTags.id);
            this.showDeleteConfirmationModal = true;
            this.showMoreTagsModal = false; // 关闭更多标签弹窗
        }
    }

    // 获取显示的三级产品标签（最多4个）
    getDisplayedLevel3Products(levelThreeProducts) {
        return levelThreeProducts ? levelThreeProducts.slice(0, 4) : [];
    }

    // 获取剩余的三级产品标签数量
    getRemainingLevel3Count(levelThreeProducts) {
        return levelThreeProducts ? Math.max(0, levelThreeProducts.length - 4) : 0;
    }

    // 检查是否需要显示"更多"按钮
    shouldShowMoreButton(levelThreeProducts) {
        return levelThreeProducts && levelThreeProducts.length > 4;
    }

    // 处理单产品"更多"标签按钮点击
    handleShowMoreTagsForSingle() {
        this.currentGroupForMoreTags = {
            allLevelThreeProducts: this.singleProductTags.allLevelThreeProducts || []
        };
        this.showMoreTagsModal = true;
        console.log('显示单产品更多标签弹窗');
    }

    // 从单产品中删除三级产品
    handleRemoveLevelThreeFromSingle(event) {
        const level3NameToDelete = event.target.dataset.level3Name;
        console.log('删除单产品三级产品:', level3NameToDelete);

        // 过滤掉被删除的产品
        this.singleProducts = this.singleProducts.filter(product => product.level3 !== level3NameToDelete);

        // 重新计算单产品的三级产品标签
        this.updateSingleProductTags();

        // 显示成功提示
        this.dispatchEvent(
            new ShowToastEvent({
                title: '成功',
                message: '三级产品目录下的所有产品已删除',
                variant: 'success'
            })
        );
    }

    // 更新单产品的三级产品标签
    updateSingleProductTags() {
        const processedProducts = this.processProductData(this.singleProducts);
        const maasLevelThreeProducts = processedProducts.filter(p => p.level1 === 'MaaS');
        const levelThreeProductNames = [...new Set(maasLevelThreeProducts.map(p => p.level3).filter(Boolean))];

        // 处理三级产品标签显示逻辑
        const allLevelThreeProducts = levelThreeProductNames.map(name => ({ name, id: 'single_' + name }));
        const displayedLevelThreeProducts = allLevelThreeProducts.slice(0, 4).map(product => ({ ...product, shouldDisplay: true }));
        const hasMoreTags = allLevelThreeProducts.length > 4;
        const remainingCount = Math.max(0, allLevelThreeProducts.length - 4);
        const moreTagsLabel = `更多 (${remainingCount})`;

        this.singleProductTags = {
            levelThreeProducts: displayedLevelThreeProducts,
            allLevelThreeProducts: allLevelThreeProducts,
            hasMoreTags: hasMoreTags,
            moreTagsLabel: moreTagsLabel
        };
    }
}